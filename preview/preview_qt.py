#!/usr/bin/env python3
"""
PyQt5-based document viewer with smooth zoom/pan and reliable bounding box overlays.
Superior alternative to matplotlib-based preview.
"""
from typing import Any

import numpy as np
from PyQt5.QtWidgets import (QApplication, QMainWindow, QGraphicsView, QGraphicsScene,
                             QGraphicsPixmapItem, QGraphicsRectItem, QGraphicsTextItem,
                             QVBoxLayout, QWidget, QPushButton, QHBoxLayout, QLabel,
                             QComboBox, QGraphicsProxyWidget, QMenu)
from PyQt5.QtCore import Qt
from PyQt5.QtGui import QPixmap, QImage, QPen, QBrush, QColor, QFont, QPainter
from pdf2image import convert_from_path
import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), '..'))
import utils.utils as utils


class InteractiveBoundingBox(QGraphicsRectItem):
    """Interactive bounding box that can be right-clicked to select class."""
    
    def __init__(self, x, y, w, h, index, parent=None):
        super().__init__(x, y, w, h, parent)
        self.index = index
        self.setAcceptHoverEvents(True)
        self.setFlag(QGraphicsRectItem.ItemIsSelectable, True)
        
    def contextMenuEvent(self, event):
        """Show context menu on right click."""
        menu = QMenu()
        menu.addAction("Set Class", self.on_set_class)
        menu.exec_(event.screenPos())
        
    def on_set_class(self):
        """Open class selection dialog."""
        # This will be connected to the viewer's class selection method
        if hasattr(self.scene(), "parent_view") and hasattr(self.scene().parent_view, "show_class_selector"):
            self.scene().parent_view.show_class_selector(self.index)

class DocumentViewer(QGraphicsView):
    """Custom QGraphicsView with smooth zoom and pan capabilities."""
    
    def __init__(self):
        super().__init__()
        
        # Setup scene
        self.scene = QGraphicsScene()
        self.setScene(self.scene)
        self.scene.parent_view = self  # Reference to this view for callbacks
        
        # Přidáme DataFrame pro ukládání dat
        self.df = None

        # Dostupné třídy pro klasifikaci - načteme z centrálního číselníku
        self.available_classes = utils.get_available_result_classes()

        # Aktivní výběrové seznamy (pro správu jejich životního cyklu)
        self.active_selectors = []
        # Uložíme původní pozice a velikosti pro správné škálování
        self.selector_original_data = []  # [(proxy, original_x, original_y, original_width, element_index)]
        
        # Configure view
        self.setDragMode(QGraphicsView.RubberBandDrag)
        self.setRenderHints(QPainter.Antialiasing | QPainter.SmoothPixmapTransform)
        self.setTransformationAnchor(QGraphicsView.AnchorUnderMouse)
        self.setResizeAnchor(QGraphicsView.AnchorUnderMouse)

        # Povolíme interakci s proxy widgety
        self.setInteractive(True)
        
        # Zoom settings
        self.zoom_factor = 1.5
        self.min_zoom = 0.1
        self.max_zoom = 10.0
        self.current_zoom = 1.0
        
        # Image and overlay items
        self.image_item = None
        self.bbox_items = []
        self.text_items = []
    
    def wheelEvent(self, event):
        """Handle mouse wheel zoom."""
        # Get zoom direction
        if event.angleDelta().y() > 0:
            zoom_in = True
        else:
            zoom_in = False
        
        # Calculate new zoom level
        if zoom_in:
            new_zoom = self.current_zoom * self.zoom_factor
        else:
            new_zoom = self.current_zoom / self.zoom_factor
        
        # Apply zoom limits
        new_zoom = max(self.min_zoom, min(self.max_zoom, new_zoom))
        
        if new_zoom != self.current_zoom:
            # Apply zoom
            scale_factor = new_zoom / self.current_zoom
            self.scale(scale_factor, scale_factor)
            self.current_zoom = new_zoom
            # Update selector positions and sizes after zoom
            self.update_selectors_for_zoom()
    
    def mousePressEvent(self, event):
        """Handle mouse press for panning."""
        # Zkontrolujeme, zda se kliklo na proxy widget (combo box)
        item = self.itemAt(event.pos())
        if isinstance(item, QGraphicsProxyWidget):
            # Předáme událost proxy widgetu a nebudeme ji dále zpracovávat
            super().mousePressEvent(event)
            return

        if event.button() == Qt.MiddleButton or (event.button() == Qt.LeftButton and event.modifiers() & Qt.ControlModifier):
            self.setDragMode(QGraphicsView.ScrollHandDrag)
        super().mousePressEvent(event)

    def mouseReleaseEvent(self, event):
        """Handle mouse release."""
        # Zkontrolujeme, zda se kliklo na proxy widget (combo box)
        item = self.itemAt(event.pos())
        if isinstance(item, QGraphicsProxyWidget):
            # Předáme událost proxy widgetu a nebudeme ji dále zpracovávat
            super().mouseReleaseEvent(event)
            return

        if event.button() == Qt.MiddleButton or (event.button() == Qt.LeftButton and event.modifiers() & Qt.ControlModifier):
            self.setDragMode(QGraphicsView.RubberBandDrag)
        super().mouseReleaseEvent(event)
    
    def load_image_from_array(self, image_array):
        """Load image from numpy array."""
        # Clear existing items
        self.clear_overlays()
        
        # Convert numpy array to QImage
        if len(image_array.shape) == 3:
            height, width, channel = image_array.shape
            bytes_per_line = 3 * width
            q_image = QImage(image_array.data, width, height, bytes_per_line, QImage.Format_RGB888)
        else:
            height, width = image_array.shape
            bytes_per_line = width
            q_image = QImage(image_array.data, width, height, bytes_per_line, QImage.Format_Grayscale8)
        
        # Convert to pixmap and add to scene
        pixmap = QPixmap.fromImage(q_image)
        
        if self.image_item:
            self.scene.removeItem(self.image_item)
        
        self.image_item = QGraphicsPixmapItem(pixmap)
        self.scene.addItem(self.image_item)
        
        # Fit image in view
        self.fitInView(self.image_item, Qt.KeepAspectRatio)
        self.current_zoom = 1.0
    
    def add_bounding_boxes(self, df):
        """Add interactive bounding boxes and labels from DataFrame."""
        self.clear_overlays()
        self.df = df.copy()  # Store DataFrame for later updates

        # Inicializujeme result_class sloupec pokud neexistuje
        if 'result_class' not in self.df.columns:
            self.df['result_class'] = 0  # Výchozí hodnota 0 místo None

        # Zajistíme, že result_class sloupec je int typ - nejprve nahradíme NaN hodnoty
        self.df['result_class'] = self.df['result_class'].fillna(0).astype(int)

        # Iterate over actual DataFrame indices instead of range(len(df))
        for i in df.index:
            text = df.loc[i, 'text']
            x = df.loc[i, 'left']
            y = df.loc[i, 'top']
            w = df.loc[i, 'width']
            h = df.loc[i, 'height']
            similarity = df.loc[i, 'similarity'] if 'similarity' in df.columns else 0
            key_class = df.loc[i, 'key_class'] if 'key_class' in df.columns else 0
            key_class_name = df.loc[i, 'key_class_name'] if 'key_class_name' in df.columns else None #utils.get_key_class_name(key_class)
            value_class = df.loc[i, 'value_class'] if 'value_class' in df.columns else 0
            result_class = df.loc[i, 'result_class'] if 'result_class' in df.columns else 0

            # Create interactive bounding box
            rect_item = InteractiveBoundingBox(x, y, w, h, i)
            pen = QPen(QColor(0, 255, 0), 3)
            pen.setCosmetic(True)
            rect_item.setPen(pen)
            rect_item.setBrush(QBrush(QColor(0, 255, 0, 30)))
            self.scene.addItem(rect_item)
            self.bbox_items.append(rect_item)

            # Add key_class label (red, above box)
            if key_class > 0:
                #key_class_name = utils.get_key_class_name(key_class)

                # Zajistíme, že popisek bude viditelný (minimálně y=10)
                label_y = max(10, y - 30)

                key_text = QGraphicsTextItem()
                key_text.setPos(x, label_y)
                # Červený popisek s bílým pozadím pro lepší čitelnost
                key_text.setHtml(f'<div style="background-color: rgba(255,255,255,230); padding: 4px; border: 2px solid red; color: red; font-weight: bold; font-size: 14px; font-family: Arial;">{key_class_name} {similarity:.2f}</div>')
                self.scene.addItem(key_text)
                self.text_items.append(key_text)

            # Add value_class label (blue, below box)
            if value_class > 0:
                value_class_name = utils.get_value_class_name(value_class)
                value_text = QGraphicsTextItem(value_class_name)
                value_text.setPos(x, y + h + 5)
                value_text.setDefaultTextColor(QColor(0, 0, 255))  # Blue
                font = QFont("Arial", 18, QFont.Bold)
                value_text.setFont(font)
                # Add background for better visibility
                value_text.setHtml(f'<div style="background-color: rgba(255,255,255,200); padding: 2px; border: 1px solid blue;">{value_class_name}</div>')
                self.scene.addItem(value_text)
                self.text_items.append(value_text)

                # Přidáme výběrový seznam vlevo od bounding boxu pro prvky s value_class > 0
                self.add_selector_for_element(i, x, y, w, h, result_class)
    
    def add_selector_for_element(self, index, x, y, w, h, current_result_class):
        """Přidá výběrový seznam vlevo od bounding boxu."""
        # Vytvoříme combo box s dostupnými třídami
        combo = QComboBox()
        combo.addItem("-- Vyberte --", 0)  # Výchozí hodnota 0

        # Přidáme všechny dostupné třídy (číslo, text)
        for class_id, class_text in self.available_classes:
            combo.addItem(class_text, class_id)

        # Nastavíme minimální šířku pro lepší viditelnost
        combo.setMinimumWidth(200)
        combo.setMaximumWidth(300)

        # Nastavíme základní font a styling
        font = QFont("Arial", 10)
        combo.setFont(font)
        combo.setStyleSheet("""
            QComboBox {
                font-size: 10px;
                padding: 2px;
                border: 1px solid gray;
                background-color: white;
                color: black;
            }
            QComboBox::drop-down {
                border: none;
                width: 20px;
                background-color: lightgray;
            }
            QComboBox::down-arrow {
                width: 12px;
                height: 12px;
            }
            QComboBox QAbstractItemView {
                background-color: white;
                color: black;
                border: 1px solid gray;
                selection-background-color: lightblue;
                selection-color: black;
            }
        """)

        # Nastavíme aktuální hodnotu pokud existuje
        if current_result_class is not None and current_result_class != 0:
            # Najdeme index podle hodnoty (class_id)
            for i in range(combo.count()):
                if combo.itemData(i) == current_result_class:
                    combo.setCurrentIndex(i)
                    break

        # Umístíme combo box vpravo od bounding boxu
        selector_x =  x + w + 10
        selector_y = y - 10

        # Vytvoříme proxy widget pro přidání do scény
        proxy = QGraphicsProxyWidget()
        proxy.setWidget(combo)
        proxy.setPos(selector_x, selector_y)

        # Uložíme původní data pro škálování
        original_width = combo.minimumWidth()
        self.selector_original_data.append((proxy, selector_x, selector_y, original_width, index))

        # Nastavíme správné flags pro interakci
        proxy.setFlag(QGraphicsProxyWidget.ItemIsSelectable, True)
        proxy.setFlag(QGraphicsProxyWidget.ItemIsFocusable, True)
        proxy.setAcceptHoverEvents(True)

        # Zajistíme, že proxy widget bude přijímat události myši
        proxy.setAcceptedMouseButtons(Qt.LeftButton | Qt.RightButton)

        # Nastavíme vysokou z-hodnotu, aby byl combo box vždy nahoře
        proxy.setZValue(1000)

        self.scene.addItem(proxy)

        # Uložíme referenci pro pozdější vyčištění
        self.active_selectors.append(proxy)

        # Připojíme signál pro aktualizaci - používáme currentIndexChanged pro získání data
        combo.currentIndexChanged.connect(lambda idx: self.update_result_class(index, combo.itemData(idx)))

    def clear_overlays(self):
        """Clear all bounding boxes and text overlays."""
        for item in self.bbox_items:
            self.scene.removeItem(item)
        for item in self.text_items:
            self.scene.removeItem(item)
        for item in self.active_selectors:
            self.scene.removeItem(item)
        self.bbox_items.clear()
        self.text_items.clear()
        self.active_selectors.clear()
        self.selector_original_data.clear()
    
    def update_selectors_for_zoom(self):
        """Update selector positions and sizes based on current zoom level."""
        if not self.selector_original_data:
            return

        for proxy, original_x, original_y, original_width, element_index in self.selector_original_data:
            if proxy and proxy.widget():
                combo = proxy.widget()

                # Škálujeme pozici podle aktuálního zoomu
                # Pozice se škáluje automaticky s transformací scény, ale velikost ne

                # Škálujeme šířku podle zoomu - menší zoom = menší combo box
                # Ale zajistíme minimální čitelnou velikost
                scaled_width = max(150, int(original_width * self.current_zoom))
                scaled_width = min(400, scaled_width)  # Maximum pro velmi velký zoom

                combo.setMinimumWidth(scaled_width)
                combo.setMaximumWidth(scaled_width + 50)

                # Škálujeme font podle zoomu pro lepší čitelnost
                font_size = max(8, min(14, int(10 * self.current_zoom)))
                font = QFont("Arial", font_size)
                combo.setFont(font)

                # Aktualizujeme stylesheet pro lepší viditelnost při různých zoom úrovních
                combo.setStyleSheet(f"""
                    QComboBox {{
                        font-size: {font_size}px;
                        padding: 2px;
                        border: 1px solid gray;
                        background-color: white;
                        color: black;
                    }}
                    QComboBox::drop-down {{
                        border: none;
                        width: 20px;
                        background-color: lightgray;
                    }}
                    QComboBox::down-arrow {{
                        width: 12px;
                        height: 12px;
                    }}
                    QComboBox QAbstractItemView {{
                        background-color: white;
                        color: black;
                        border: 1px solid gray;
                        selection-background-color: lightblue;
                        selection-color: black;
                    }}
                """)

    def reset_view(self):
        """Reset zoom and center the image."""
        if self.image_item:
            self.fitInView(self.image_item, Qt.KeepAspectRatio)
            self.current_zoom = 1.0
            # Update selectors after reset
            self.update_selectors_for_zoom()

    def update_result_class(self, index, class_id):
        """Aktualizuje result_class pro daný index v DataFrame."""
        if self.df is not None and index in self.df.index:
            # Převedeme class_id na int
            try:
                class_id_int = int(class_id) if class_id is not None else 0
            except (ValueError, TypeError):
                class_id_int = 0

            # Aktualizujeme DataFrame
            self.df.loc[index, 'result_class'] = class_id_int

    def get_updated_dataframe(self):
        """Vrátí aktualizovaný DataFrame s result_class sloupcem."""
        return self.df.copy() if self.df is not None else None

    def show_class_selector(self, index):
        """Show class selector for the given bounding box index (legacy method for compatibility)."""
        # Tato metoda je zachována pro kompatibilitu s InteractiveBoundingBox
        # ale nyní používáme stálé výběrové seznamy
        pass

class DocumentViewerWindow(QMainWindow):
    """Main window for document viewing."""
    
    def __init__(self):
        super().__init__()
        self.setWindowTitle("Document Viewer - OCR Results")
        self.setGeometry(100, 100, 1200, 800)
        
        # Create central widget
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # Create layout
        layout = QVBoxLayout(central_widget)
        
        # Create toolbar
        toolbar = QHBoxLayout()

        self.zoom_label = QLabel("Zoom: 100%")
        toolbar.addWidget(self.zoom_label)

        # Zoom buttons
        zoom_in_btn = QPushButton("Zoom In (+)")
        zoom_in_btn.clicked.connect(self.zoom_in)
        toolbar.addWidget(zoom_in_btn)

        zoom_out_btn = QPushButton("Zoom Out (-)")
        zoom_out_btn.clicked.connect(self.zoom_out)
        toolbar.addWidget(zoom_out_btn)

        reset_btn = QPushButton("Reset View")
        reset_btn.clicked.connect(self.reset_view)
        toolbar.addWidget(reset_btn)

        # Toggle bounding boxes
        self.toggle_bbox_btn = QPushButton("Hide Boxes")
        self.toggle_bbox_btn.clicked.connect(self.toggle_bounding_boxes)
        toolbar.addWidget(self.toggle_bbox_btn)

        toolbar.addStretch()

        info_label = QLabel("Wheel: zoom | Middle/Ctrl+drag: pan | Buttons: controls")
        toolbar.addWidget(info_label)
        
        layout.addLayout(toolbar)
        
        # Create viewer
        self.viewer = DocumentViewer()
        layout.addWidget(self.viewer)

        # Connect zoom updates
        self.viewer.scene.changed.connect(self.update_zoom_label)


    
    def load_document(self, file_path, df):
        """Load document and display with bounding boxes."""
        # Convert PDF to image
        images = convert_from_path(file_path, dpi=300, output_folder=None)

        if images:
            # Convert PIL image to numpy array
            image = np.array(images[0])

            # Load image
            self.viewer.load_image_from_array(image)

            # Add bounding boxes
            self.viewer.add_bounding_boxes(df)

            self.setWindowTitle(f"Document Viewer - {file_path}")
    
    def zoom_in(self):
        """Zoom in programmatically."""
        new_zoom = self.viewer.current_zoom * self.viewer.zoom_factor
        new_zoom = min(self.viewer.max_zoom, new_zoom)
        if new_zoom != self.viewer.current_zoom:
            scale_factor = new_zoom / self.viewer.current_zoom
            self.viewer.scale(scale_factor, scale_factor)
            self.viewer.current_zoom = new_zoom
            self.viewer.update_selectors_for_zoom()
            self.update_zoom_label()

    def zoom_out(self):
        """Zoom out programmatically."""
        new_zoom = self.viewer.current_zoom / self.viewer.zoom_factor
        new_zoom = max(self.viewer.min_zoom, new_zoom)
        if new_zoom != self.viewer.current_zoom:
            scale_factor = new_zoom / self.viewer.current_zoom
            self.viewer.scale(scale_factor, scale_factor)
            self.viewer.current_zoom = new_zoom
            self.viewer.update_selectors_for_zoom()
            self.update_zoom_label()

    def reset_view(self):
        """Reset view to fit image."""
        self.viewer.reset_view()
        self.update_zoom_label()

    def toggle_bounding_boxes(self):
        """Toggle visibility of bounding boxes."""
        visible = len(self.viewer.bbox_items) > 0 and self.viewer.bbox_items[0].isVisible()

        for item in self.viewer.bbox_items + self.viewer.text_items:
            item.setVisible(not visible)

        self.toggle_bbox_btn.setText("Show Boxes" if visible else "Hide Boxes")

    def update_zoom_label(self):
        """Update zoom percentage in toolbar."""
        zoom_percent = int(self.viewer.current_zoom * 100)
        self.zoom_label.setText(f"Zoom: {zoom_percent}%")



def show_document_qt(file_path: object, df: object, return_results: object = False) -> Any | None:
    """
    Show document with OCR results using PyQt5 viewer.

    Args:
        file_path (str): Path to PDF file
        df (pandas.DataFrame): DataFrame with OCR results
        return_results (bool): If True, returns updated DataFrame after user interaction

    Returns:
        pandas.DataFrame or None: Updated DataFrame with result_class column if return_results=True
    """
    app = QApplication.instance()
    if app is None:
        app = QApplication(sys.argv)

    viewer = DocumentViewerWindow()
    viewer.load_document(file_path, df)
    viewer.show()

    app.exec_()
    return
    # Vrátíme aktualizovaný DataFrame pokud je požadován
    if return_results:
        return viewer.viewer.get_updated_dataframe()
    return None

def show_ocr_result_qt(file_path, df, return_results=False):
    """
    Show OCR results using PyQt5 viewer (same as show_document_qt for consistency).

    Args:
        file_path (str): Path to PDF file
        df (pandas.DataFrame): DataFrame with OCR results
        return_results (bool): If True, returns updated DataFrame after user interaction

    Returns:
        pandas.DataFrame or None: Updated DataFrame with result_class column if return_results=True
    """
    return show_document_qt(file_path, df, return_results)

if __name__ == "__main__":
    # Test the viewer
    from OCR import ocr_old

    print("Testing PyQt5 Document Viewer...")

    # Load and process data
    df = ocr.do('Data/F3.pdf')
    utils.classify_batch_values(df)
    from OCR import postprocess
    df = postprocess.clean_texts(df)

    # Simulace key klasifikace pro test (v reálném použití by se volala skutečná klasifikace)
    df['key_class'] = 0
    df['similarity'] = 0.0

    # Odstranění nevalidních textů po obou klasifikacích
    df = utils.remove_invalid_texts(df)

    print(f"Loaded {len(df)} text elements")
    print("Opening PyQt5 viewer...")

    show_document_qt('Data/F3.pdf', df, return_results=True)
