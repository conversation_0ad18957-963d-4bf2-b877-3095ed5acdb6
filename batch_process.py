#!/usr/bin/env python3
"""
Batch zpracování více dokumentů najednou.

Tento skript umožňuje zpracovat všechny PDF dokumenty ve složce Data
nebo seznam konkrétních souborů.

Použití:
    python batch_process.py                    # Zpracuje všechny PDF v Data/
    python batch_process.py --folder Custom/   # Zpracuje všechny PDF v Custom/
    python batch_process.py file1.pdf file2.pdf  # Zpracuje konkrétní soubory
"""

import os
import sys
import argparse
import glob
from pathlib import Path
import time
from datetime import datetime

# Import našeho process_document modulu
from process_document import process_document, analyze_results, save_results


def find_pdf_files(folder_path):
    """
    Najde všechny PDF soubory ve složce.
    
    Args:
        folder_path (str): Cesta ke složce
        
    Returns:
        list: Seznam cest k PDF souborům
    """
    if not os.path.exists(folder_path):
        raise FileNotFoundError(f"Složka {folder_path} neexistuje")
    
    pdf_pattern = os.path.join(folder_path, "*.pdf")
    pdf_files = glob.glob(pdf_pattern)
    
    # Seřadíme podle názvu
    pdf_files.sort()
    
    return pdf_files


def process_batch(file_paths, use_gat=False, interactive=False, continue_on_error=True):
    """
    Zpracuje batch souborů.
    
    Args:
        file_paths (list): Seznam cest k PDF souborům
        use_gat (bool): Zda použít GAT klasifikaci
        interactive (bool): Zda zobrazit interaktivní viewer
        continue_on_error (bool): Zda pokračovat při chybě
        
    Returns:
        dict: Statistiky zpracování
    """
    
    print("🚀 Batch zpracování dokumentů")
    print("=" * 60)
    print(f"📄 Počet souborů: {len(file_paths)}")
    print(f"🧠 GAT klasifikace: {'Zapnuta' if use_gat else 'Vypnuta'}")
    print(f"🖥️  Interaktivní režim: {'Zapnut' if interactive else 'Vypnut'}")
    print(f"🔄 Pokračovat při chybě: {'Ano' if continue_on_error else 'Ne'}")
    print()
    
    # Statistiky
    stats = {
        'total': len(file_paths),
        'processed': 0,
        'failed': 0,
        'skipped': 0,
        'start_time': time.time(),
        'results': []
    }
    
    for i, file_path in enumerate(file_paths, 1):
        print(f"\n{'='*60}")
        print(f"📄 Zpracování {i}/{len(file_paths)}: {os.path.basename(file_path)}")
        print(f"{'='*60}")
        
        try:
            # Kontrola, zda výsledek už existuje
            file_name = Path(file_path).stem
            result_file = f"Results/{file_name}.csv"
            
            if os.path.exists(result_file):
                print(f"⚠️  Výsledek už existuje: {result_file}")
                response = input("Přepsat? (y/n/s pro skip): ").lower().strip()
                
                if response == 's':
                    print("   ⏭️  Přeskakuji...")
                    stats['skipped'] += 1
                    continue
                elif response != 'y':
                    print("   ⏭️  Přeskakuji...")
                    stats['skipped'] += 1
                    continue
            
            # Zpracování dokumentu
            start_time = time.time()
            df = process_document(
                file_path=file_path,
                use_gat=use_gat,
                interactive=interactive
            )
            
            # Analýza výsledků
            analyze_results(df)
            
            # Uložení výsledků
            output_file = save_results(df, file_path)
            
            processing_time = time.time() - start_time
            
            # Uložení statistik
            file_stats = {
                'file': file_path,
                'output': output_file,
                'processing_time': processing_time,
                'elements_total': len(df) if df is not None else 0,
                'elements_key_classified': len(df[df['key_class'] > 0]) if df is not None and 'key_class' in df.columns else 0,
                'elements_result_classified': len(df[df['result_class'] > 0]) if df is not None and 'result_class' in df.columns else 0,
                'status': 'success'
            }
            
            stats['results'].append(file_stats)
            stats['processed'] += 1
            
            print(f"\n✅ Soubor zpracován za {processing_time:.1f}s")
            
        except KeyboardInterrupt:
            print(f"\n\n⚠️  Batch zpracování přerušeno uživatelem")
            stats['failed'] += 1
            break
            
        except Exception as e:
            print(f"\n❌ Chyba při zpracování {file_path}: {e}")
            
            file_stats = {
                'file': file_path,
                'output': None,
                'processing_time': 0,
                'elements_total': 0,
                'elements_key_classified': 0,
                'elements_result_classified': 0,
                'status': 'failed',
                'error': str(e)
            }
            
            stats['results'].append(file_stats)
            stats['failed'] += 1
            
            if not continue_on_error:
                print("Ukončuji batch zpracování kvůli chybě.")
                break
            else:
                print("Pokračuji s dalším souborem...")
    
    return stats


def print_batch_summary(stats):
    """
    Vypíše souhrn batch zpracování.
    
    Args:
        stats (dict): Statistiky zpracování
    """
    total_time = time.time() - stats['start_time']
    
    print(f"\n{'='*60}")
    print("📊 SOUHRN BATCH ZPRACOVÁNÍ")
    print(f"{'='*60}")
    print(f"⏱️  Celkový čas: {total_time:.1f}s ({total_time/60:.1f} min)")
    print(f"📄 Celkem souborů: {stats['total']}")
    print(f"✅ Úspěšně zpracováno: {stats['processed']}")
    print(f"❌ Chyby: {stats['failed']}")
    print(f"⏭️  Přeskočeno: {stats['skipped']}")
    
    if stats['processed'] > 0:
        avg_time = sum(r['processing_time'] for r in stats['results'] if r['status'] == 'success') / stats['processed']
        total_elements = sum(r['elements_total'] for r in stats['results'] if r['status'] == 'success')
        total_key_classified = sum(r['elements_key_classified'] for r in stats['results'] if r['status'] == 'success')
        total_result_classified = sum(r['elements_result_classified'] for r in stats['results'] if r['status'] == 'success')
        
        print(f"\n📈 STATISTIKY:")
        print(f"   Průměrný čas na soubor: {avg_time:.1f}s")
        print(f"   Celkem prvků: {total_elements}")
        print(f"   Key klasifikované: {total_key_classified}")
        print(f"   Result klasifikované: {total_result_classified}")
    
    # Detail úspěšných zpracování
    successful = [r for r in stats['results'] if r['status'] == 'success']
    if successful:
        print(f"\n✅ ÚSPĚŠNĚ ZPRACOVANÉ SOUBORY:")
        for result in successful:
            file_name = os.path.basename(result['file'])
            print(f"   {file_name}: {result['elements_total']} prvků, "
                  f"{result['elements_key_classified']} key, "
                  f"{result['elements_result_classified']} result "
                  f"({result['processing_time']:.1f}s)")
    
    # Detail chyb
    failed = [r for r in stats['results'] if r['status'] == 'failed']
    if failed:
        print(f"\n❌ CHYBY:")
        for result in failed:
            file_name = os.path.basename(result['file'])
            error = result.get('error', 'Neznámá chyba')
            print(f"   {file_name}: {error}")


def main():
    """Hlavní funkce skriptu."""

    # ========================================
    # KONFIGURACE PRO SPUŠTĚNÍ Z IDE
    # ========================================

    # Nastavte zde parametry pro spuštění z IDE:
    IDE_MODE = True  # Změňte na False pro použití command line argumentů

    if IDE_MODE:
        # Parametry pro IDE režim - upravte podle potřeby:

        # Možnost 1: Zpracovat konkrétní soubory
        FILES = ["Data/F1.pdf", "Data/F2.pdf"]  # Seznam konkrétních souborů

        # Možnost 2: Zpracovat celou složku (nastavte FILES = [] pro použití složky)
        # FILES = []
        FOLDER = "Data"                    # Složka s PDF soubory

        USE_GAT = False                    # True = použít GAT klasifikaci
        INTERACTIVE = False                # True = použít interaktivní viewer
        STOP_ON_ERROR = False              # True = zastavit při první chybě

        print("🖥️  Spuštěno v IDE režimu")
        if FILES:
            print(f"📄 Soubory: {FILES}")
        else:
            print(f"📁 Složka: {FOLDER}")
        print(f"🧠 GAT: {'Zapnuto' if USE_GAT else 'Vypnuto'}")
        print(f"🖱️  Interaktivní: {'Zapnuto' if INTERACTIVE else 'Vypnuto'}")
        print(f"🛑 Zastavit při chybě: {'Ano' if STOP_ON_ERROR else 'Ne'}")
        print()

        # Použijeme IDE parametry
        files = FILES
        folder = FOLDER
        use_gat = USE_GAT
        interactive = INTERACTIVE
        stop_on_error = STOP_ON_ERROR

    else:
        # Command line režim
        parser = argparse.ArgumentParser(
            description='Batch zpracování PDF dokumentů',
            formatter_class=argparse.RawDescriptionHelpFormatter,
            epilog="""
Příklady použití:
  python batch_process.py                           # Všechny PDF v Data/
  python batch_process.py --folder Custom/         # Všechny PDF v Custom/
  python batch_process.py file1.pdf file2.pdf     # Konkrétní soubory
  python batch_process.py --gat                    # S GAT klasifikací
  python batch_process.py --interactive            # S interaktivním režimem
            """
        )

        parser.add_argument('files', nargs='*', help='Konkrétní PDF soubory k zpracování')
        parser.add_argument('--folder', default='Data', help='Složka s PDF soubory (default: Data)')
        parser.add_argument('--gat', action='store_true', help='Použít GAT klasifikaci')
        parser.add_argument('--interactive', action='store_true', help='Použít interaktivní viewer')
        parser.add_argument('--stop-on-error', action='store_true', help='Zastavit při první chybě')

        args = parser.parse_args()

        # Použijeme command line argumenty
        files = args.files
        folder = args.folder
        use_gat = args.gat
        interactive = args.interactive
        stop_on_error = args.stop_on_error
    
    try:
        # Určíme seznam souborů k zpracování
        if files:
            # Konkrétní soubory
            file_paths = files
            # Kontrola existence
            for file_path in file_paths:
                if not os.path.exists(file_path):
                    raise FileNotFoundError(f"Soubor {file_path} neexistuje")
        else:
            # Všechny PDF ve složce
            file_paths = find_pdf_files(folder)
            if not file_paths:
                print(f"⚠️  Ve složce {folder} nebyly nalezeny žádné PDF soubory")
                return

        print(f"Nalezeno {len(file_paths)} PDF souborů k zpracování")

        # Potvrzení od uživatele (pouze v command line režimu)
        if not IDE_MODE and len(file_paths) > 1:
            response = input(f"Zpracovat {len(file_paths)} souborů? (y/n): ").lower().strip()
            if response != 'y':
                print("Batch zpracování zrušeno")
                return
        elif IDE_MODE:
            print("🚀 Spouštím batch zpracování...")

        # Batch zpracování
        stats = process_batch(
            file_paths=file_paths,
            use_gat=use_gat,
            interactive=interactive,
            continue_on_error=not stop_on_error
        )
        
        # Souhrn
        print_batch_summary(stats)
        
        # Uložení logu
        log_file = f"batch_log_{datetime.now().strftime('%Y%m%d_%H%M%S')}.txt"
        with open(log_file, 'w', encoding='utf-8') as f:
            f.write(f"Batch zpracování - {datetime.now()}\n")
            f.write(f"Celkem souborů: {stats['total']}\n")
            f.write(f"Úspěšně: {stats['processed']}\n")
            f.write(f"Chyby: {stats['failed']}\n")
            f.write(f"Přeskočeno: {stats['skipped']}\n\n")
            
            for result in stats['results']:
                f.write(f"{result['file']}: {result['status']}\n")
                if result['status'] == 'failed' and 'error' in result:
                    f.write(f"  Chyba: {result['error']}\n")
        
        print(f"\n📝 Log uložen: {log_file}")
        
    except KeyboardInterrupt:
        print("\n\n⚠️  Batch zpracování přerušeno uživatelem")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ Chyba při batch zpracování: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)


if __name__ == "__main__":
    main()
