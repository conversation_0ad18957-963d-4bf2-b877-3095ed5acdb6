# Typová matice pro GAT klasifikátor

## P<PERSON>ehled

Do metody `prepare_graph_data` byla <PERSON> **typová matice** (`type_affinity_matrix`), kter<PERSON> definuje omezující vztahy mezi klíči a entitami. Cílem je omezit možné klasifikace na základě logických vztahů mezi typy klí<PERSON> a hodnot.

## Implementace

### Nov<PERSON> funkce: `create_type_affinity_matrix`

```python
def create_type_affinity_matrix(max_key_class, max_value_class):
    """
    Vytvoří typovou matici definující omezující vztahy mezi klíči a entitami.
    
    Returns:
        np.ndarray: Matice [max_key_class+1, max_value_class+1] s hodnotami 0/1
                   kde 1 znamená povolený vztah mezi key_class a value_class
    """
```

### Integrace do `prepare_graph_data`

Typo<PERSON>á matice je integrována jako nový edge feature `type_affinity_hint`, který se přidává k existujícím edge features:

- `dx, dy, dist, angle, priority_hint, page_hint, type_affinity_hint` (celkem 7 features)

## Definovaná omezení

| Klíč (key_class) | Povolené hodnoty (value_class) | Popis |
|------------------|--------------------------------|-------|
| Číslo faktury (6) | Alfanumerický kód (5), Číslo (8) | Faktury mohou mít alfanumerické nebo číselné identifikátory |
| Variabilní symbol (7) | Číslo (8) | VS je vždy číselný |
| IČO (19) | Číslo (8) | IČO je vždy číselné |
| DIČ (21) | DIČ (7) | DIČ má specifický formát |
| Datum vystavení (13) | Datum (1) | Datum musí být ve formátu data |
| Datum splatnosti (14) | Datum (1) | Datum musí být ve formátu data |
| DUZP (15) | Datum (1) | DUZP je datum |
| Číslo účtu (12) | Číslo účtu (4) | Specifický formát čísla účtu |
| Číslo objednávky (9) | Číslo (8), Alfanumerický kód (5) | Objednávky mohou být číselné nebo alfanumerické |
| Sazba DPH (18) | Číslo (8), Procenta (2) | DPH může být číslo nebo procento |
| IBAN (22) | IBAN (6) | IBAN má specifický formát |

### Výchozí pravidla

- **Nespecifikované klíče**: Všechny ostatní klíče mohou mít pouze hodnotu typu "Číslo (8)"
- **Neznámý klíč (0)**: Může mít jakoukoliv hodnotu
- **Neznámá hodnota (0)**: Může být u jakéhokoliv klíče

## Technické detaily

### Změny v architektuře

1. **Edge features dimension**: Zvýšena z 6 na 7 (přidán `type_affinity_hint`)
2. **GAT vrstvy**: Aktualizovány pro podporu 7 edge features
3. **InterpretableGATClassifier**: Také aktualizován

### Výpočet typové afinity

```python
for i in range(num_nodes):
    for j in range(num_nodes):
        if is_key[i] and is_value[j]:
            key_class = key_classes[i]
            value_class = value_classes[j]
            if key_class <= max_key_class and value_class <= max_value_class:
                type_affinity_hint[i, j] = type_affinity_matrix[key_class, value_class]
```

## Testování

Spusťte test skript pro ověření funkčnosti:

```bash
python3 test_type_affinity.py
```

Test ověřuje:
1. Správnost vytvoření typové matice
2. Integraci do `prepare_graph_data`
3. Kompatibilitu s GAT modelem

## Přínosy

1. **Logická konzistence**: Zabraňuje nesmyslným klasifikacím (např. datum jako číslo faktury)
2. **Lepší konvergence**: Síť se může soustředit na relevantní vztahy
3. **Interpretovatelnost**: Jasně definované vztahy mezi typy dat
4. **Flexibilita**: Snadno rozšiřitelné o nová omezení

## Kompatibilita

- ✅ Kompatibilní s existujícími tréninkovými skripty
- ✅ Kompatibilní s C++ deployment (pouze přidání edge feature)
- ✅ Zpětně kompatibilní (existující modely budou potřebovat přetrénování)

## Budoucí rozšíření

- Možnost definovat váhované afinity (místo binárních 0/1)
- Dynamické načítání omezení z konfiguračního souboru
- Hierarchické vztahy mezi typy
