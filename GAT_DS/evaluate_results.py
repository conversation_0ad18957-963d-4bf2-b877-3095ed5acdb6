import os
import pandas as pd
import torch
import numpy as np
from sklearn.metrics import accuracy_score, precision_recall_fscore_support, confusion_matrix, classification_report
import matplotlib.pyplot as plt
from collections import defaultdict

from GATClassifier import InterpretableGATClassifier, calculate_node_dim, predict
import sys
sys.path.append('../utils')
from utils import RESULT_CLASS_REGISTRY, get_result_class_name

def get_device(device=None):
    if device is None:
        if torch.cuda.is_available():
            device = torch.device('cuda')
        elif hasattr(torch.backends, 'mps') and torch.backends.mps.is_available():
            device = torch.device('mps')
        else:
            device = torch.device('cpu')
    else:
        device = torch.device(device)
    return device

CONFIG = {
    'results_dir': '../Results',
    'epochs': 50,
    'learning_rate': 0.001,
    'patience': 10,
    'device': get_device(),
    'model_path': 'training_results/best_model.pth',
    'output_dir': 'evaluation_results',
    'confidence_threshold': 0.5
}

def load_files_from_results(dir_path):
    """Načte všechny CSV soubory ze složky Results"""
    data = []
    for filename in sorted(os.listdir(dir_path)):
        if filename.endswith('.csv'):
            data.append(os.path.join(dir_path, filename))
    return data

def evaluate_single_file(model, file_path, device, max_key_class, max_value_class, max_result_class):
    """Vyhodnotí jeden soubor a vrátí statistiky"""
    df = pd.read_csv(file_path)

    # Provedení predikce
    predictions, confidences = predict(model, df, device, max_key_class, max_value_class, max_result_class)

    # Aplikace prahu spolehlivosti
    confidence_threshold = CONFIG['confidence_threshold']
    filtered_predictions = predictions.copy()
    low_confidence_mask = confidences < confidence_threshold
    filtered_predictions[low_confidence_mask] = 0

    # Uložení všech výsledků do DataFrame
    df['original_predicted_class'] = predictions  # Původní predikce
    df['predicted_class'] = filtered_predictions  # Filtrované predikce
    df['confidence'] = confidences

    # Filtrování pouze anotovaných záznamů (result_class > 0)
    annotated_mask = df['result_class'] > 0
    annotated_df = df[annotated_mask]

    if len(annotated_df) == 0:
        return None, df  # Žádné anotované záznamy

    true_labels = annotated_df['result_class'].values
    pred_labels = annotated_df['predicted_class'].values  # Používáme filtrované predikce
    original_pred_labels = annotated_df['original_predicted_class'].values
    confidences_annotated = annotated_df['confidence'].values
    
    # Výpočet metrik
    accuracy = accuracy_score(true_labels, pred_labels)
    precision, recall, f1, support = precision_recall_fscore_support(
        true_labels, pred_labels, average=None, labels=range(1, max_result_class + 1), zero_division=0
    )
    
    # Celkové metriky
    macro_precision, macro_recall, macro_f1, _ = precision_recall_fscore_support(
        true_labels, pred_labels, average='macro', zero_division=0
    )
    weighted_precision, weighted_recall, weighted_f1, _ = precision_recall_fscore_support(
        true_labels, pred_labels, average='weighted', zero_division=0
    )
    
    # Matice záměn
    conf_matrix = confusion_matrix(true_labels, pred_labels, labels=range(1, max_result_class + 1))

    # Statistiky spolehlivosti
    high_confidence_annotated = (confidences_annotated >= confidence_threshold).sum()
    low_confidence_annotated = (confidences_annotated < confidence_threshold).sum()
    avg_confidence_all = confidences_annotated.mean()
    avg_confidence_classified = confidences_annotated[pred_labels > 0].mean() if (pred_labels > 0).sum() > 0 else 0
    avg_confidence_unclassified = confidences_annotated[pred_labels == 0].mean() if (pred_labels == 0).sum() > 0 else 0

    # Statistiky původních vs filtrovaných predikcí
    original_classified = (original_pred_labels > 0).sum()
    filtered_classified = (pred_labels > 0).sum()
    filtered_out_count = original_classified - filtered_classified

    # Accuracy na původních predikcích (pro porovnání)
    original_accuracy = accuracy_score(true_labels, original_pred_labels)

    stats = {
        'filename': os.path.basename(file_path),
        'total_records': len(df),
        'annotated_records': len(annotated_df),
        'confidence_threshold': confidence_threshold,

        # Základní metriky (po aplikaci prahu)
        'accuracy': accuracy,
        'macro_precision': macro_precision,
        'macro_recall': macro_recall,
        'macro_f1': macro_f1,
        'weighted_precision': weighted_precision,
        'weighted_recall': weighted_recall,
        'weighted_f1': weighted_f1,

        # Metriky spolehlivosti
        'high_confidence_annotated': high_confidence_annotated,
        'low_confidence_annotated': low_confidence_annotated,
        'avg_confidence_all': avg_confidence_all,
        'avg_confidence_classified': avg_confidence_classified,
        'avg_confidence_unclassified': avg_confidence_unclassified,

        # Porovnání původních vs filtrovaných predikcí
        'original_classified': original_classified,
        'filtered_classified': filtered_classified,
        'filtered_out_count': filtered_out_count,
        'original_accuracy': original_accuracy,

        # Detailní data
        'per_class_precision': precision,
        'per_class_recall': recall,
        'per_class_f1': f1,
        'per_class_support': support,
        'confusion_matrix': conf_matrix,
        'true_labels': true_labels,
        'pred_labels': pred_labels,
        'original_pred_labels': original_pred_labels,
        'confidences': confidences_annotated
    }
    
    return stats, df

def create_detailed_statistics(all_stats, max_result_class, output_dir, processed_files=None):
    """Vytvoří detailní statistiky a grafy"""
    os.makedirs(output_dir, exist_ok=True)
    
    # Filtrování pouze platných statistik
    valid_stats = [s for s in all_stats if s is not None]
    
    if not valid_stats:
        print("Žádné platné statistiky k vyhodnocení!")
        return
    
    # Souhrnné statistiky
    print("=" * 80)
    print("DETAILNÍ STATISTIKY KLASIFIKAČNÍ SÍTĚ")
    print("=" * 80)
    
    # Získání prahu spolehlivosti
    confidence_threshold = valid_stats[0]['confidence_threshold']

    # Statistiky pro jednotlivé soubory
    print(f"\nSTATISTIKY PRO JEDNOTLIVÉ SOUBORY (práh spolehlivosti: {confidence_threshold}):")
    print("-" * 120)
    print(f"{'Soubor':<20} {'Celkem':<8} {'Anotace':<8} {'Vysoká conf.':<12} {'Filtrováno':<11} {'Accuracy':<10} {'Orig. Acc.':<10} {'Avg Conf.':<10}")
    print("-" * 120)
    
    total_annotated = 0
    total_correct = 0
    total_high_confidence = 0
    total_filtered_out = 0
    all_true_labels = []
    all_pred_labels = []
    all_original_pred_labels = []
    all_confidences = []

    for stats in valid_stats:
        print(f"{stats['filename']:<20} {stats['total_records']:<8} {stats['annotated_records']:<8} "
              f"{stats['high_confidence_annotated']:<12} {stats['filtered_out_count']:<11} "
              f"{stats['accuracy']:<10.3f} {stats['original_accuracy']:<10.3f} {stats['avg_confidence_all']:<10.3f}")

        total_annotated += stats['annotated_records']
        total_correct += int(stats['accuracy'] * stats['annotated_records'])
        total_high_confidence += stats['high_confidence_annotated']
        total_filtered_out += stats['filtered_out_count']
        all_true_labels.extend(stats['true_labels'])
        all_pred_labels.extend(stats['pred_labels'])
        all_original_pred_labels.extend(stats['original_pred_labels'])
        all_confidences.extend(stats['confidences'])
    
    # Celkové statistiky
    overall_accuracy = total_correct / total_annotated if total_annotated > 0 else 0
    overall_original_accuracy = accuracy_score(all_true_labels, all_original_pred_labels) if all_original_pred_labels else 0
    overall_avg_confidence = np.mean(all_confidences) if all_confidences else 0

    print("-" * 120)
    print(f"{'CELKEM':<20} {sum(s['total_records'] for s in valid_stats):<8} {total_annotated:<8} "
          f"{total_high_confidence:<12} {total_filtered_out:<11} "
          f"{overall_accuracy:<10.3f} {overall_original_accuracy:<10.3f} {overall_avg_confidence:<10.3f}")

    # Souhrnné statistiky spolehlivosti
    print(f"\nSOUHRNNÉ STATISTIKY SPOLEHLIVOSTI:")
    print("-" * 80)
    print(f"Práh spolehlivosti: {confidence_threshold}")
    print(f"Celkem anotovaných záznamů: {total_annotated}")
    print(f"Záznamy s vysokou spolehlivostí: {total_high_confidence} ({total_high_confidence/total_annotated*100:.1f}%)")
    print(f"Odfiltrované záznamy: {total_filtered_out} ({total_filtered_out/total_annotated*100:.1f}%)")
    print(f"Průměrná spolehlivost: {overall_avg_confidence:.3f}")
    print(f"Accuracy po filtrování: {overall_accuracy:.3f}")
    print(f"Accuracy před filtrováním: {overall_original_accuracy:.3f}")
    print(f"Rozdíl v accuracy: {overall_accuracy - overall_original_accuracy:+.3f}")
    
    # Detailní analýza po třídách
    print(f"\nDETAILNÍ ANALÝZA PO TŘÍDÁCH:")
    print("-" * 80)
    
    # Celkové metriky napříč všemi soubory
    overall_precision, overall_recall, overall_f1, overall_support = precision_recall_fscore_support(
        all_true_labels, all_pred_labels, average=None, labels=range(1, max_result_class + 1), zero_division=0
    )
    
    print(f"{'Třída':<8} {'Název':<30} {'Precision':<12} {'Recall':<12} {'F1-Score':<12} {'Support':<10} {'Avg Conf.':<10}")
    print("-" * 110)

    # Výpočet průměrné spolehlivosti pro každou třídu
    class_confidences = {}
    for i, (true_label, pred_label, confidence) in enumerate(zip(all_true_labels, all_pred_labels, all_confidences)):
        if pred_label > 0:  # Pouze klasifikované záznamy
            if pred_label not in class_confidences:
                class_confidences[pred_label] = []
            class_confidences[pred_label].append(confidence)

    for i in range(max_result_class):
        class_num = i + 1
        class_name = get_result_class_name(class_num)
        avg_conf = np.mean(class_confidences.get(class_num, [0])) if class_confidences.get(class_num) else 0
        print(f"{class_num:<8} {class_name:<30} {overall_precision[i]:<12.3f} {overall_recall[i]:<12.3f} "
              f"{overall_f1[i]:<12.3f} {overall_support[i]:<10} {avg_conf:<10.3f}")
    
    # Makro a vážené průměry
    macro_prec = np.mean(overall_precision)
    macro_rec = np.mean(overall_recall)
    macro_f1 = np.mean(overall_f1)
    
    weighted_prec = np.average(overall_precision, weights=overall_support)
    weighted_rec = np.average(overall_recall, weights=overall_support)
    weighted_f1 = np.average(overall_f1, weights=overall_support)
    
    # Průměrná spolehlivost všech klasifikovaných záznamů
    overall_classified_confidence = np.mean([conf for conf in all_confidences if conf > 0]) if all_confidences else 0

    print("-" * 110)
    print(f"{'Macro avg':<8} {'(průměr všech tříd)':<30} {macro_prec:<12.3f} {macro_rec:<12.3f} {macro_f1:<12.3f} {sum(overall_support):<10} {overall_classified_confidence:<10.3f}")
    print(f"{'Weighted avg':<8} {'(vážený průměr)':<30} {weighted_prec:<12.3f} {weighted_rec:<12.3f} {weighted_f1:<12.3f} {sum(overall_support):<10} {overall_classified_confidence:<10.3f}")
    
    # Uložení CSV se statistikami pro jednotlivé soubory
    file_stats_df = pd.DataFrame([
        {
            'filename': stats['filename'],
            'total_records': stats['total_records'],
            'annotated_records': stats['annotated_records'],
            'confidence_threshold': stats['confidence_threshold'],
            'high_confidence_annotated': stats['high_confidence_annotated'],
            'low_confidence_annotated': stats['low_confidence_annotated'],
            'filtered_out_count': stats['filtered_out_count'],
            'accuracy': stats['accuracy'],
            'original_accuracy': stats['original_accuracy'],
            'avg_confidence_all': stats['avg_confidence_all'],
            'avg_confidence_classified': stats['avg_confidence_classified'],
            'avg_confidence_unclassified': stats['avg_confidence_unclassified'],
            'macro_precision': stats['macro_precision'],
            'macro_recall': stats['macro_recall'],
            'macro_f1': stats['macro_f1'],
            'weighted_precision': stats['weighted_precision'],
            'weighted_recall': stats['weighted_recall'],
            'weighted_f1': stats['weighted_f1']
        }
        for stats in valid_stats
    ])
    file_stats_df.to_csv(os.path.join(output_dir, 'file_statistics.csv'), index=False)

    # Uložení CSV se statistikami pro jednotlivé třídy
    class_avg_confidences = []
    for i in range(1, max_result_class + 1):
        avg_conf = np.mean(class_confidences.get(i, [0])) if class_confidences.get(i) else 0
        class_avg_confidences.append(avg_conf)

    class_stats_df = pd.DataFrame({
        'class': range(1, max_result_class + 1),
        'class_name': [get_result_class_name(i) for i in range(1, max_result_class + 1)],
        'precision': overall_precision,
        'recall': overall_recall,
        'f1_score': overall_f1,
        'support': overall_support,
        'avg_confidence': class_avg_confidences
    })
    class_stats_df.to_csv(os.path.join(output_dir, 'class_statistics.csv'), index=False)

    # Uložení detailního reportu
    with open(os.path.join(output_dir, 'detailed_report.txt'), 'w', encoding='utf-8') as f:
        f.write("DETAILNÍ STATISTIKY KLASIFIKAČNÍ SÍTĚ\n")
        f.write("=" * 80 + "\n\n")

        f.write(f"Práh spolehlivosti: {confidence_threshold}\n\n")

        f.write("STATISTIKY PRO JEDNOTLIVÉ SOUBORY:\n")
        f.write("-" * 120 + "\n")
        f.write(f"{'Soubor':<20} {'Celkem':<8} {'Anotace':<8} {'Vysoká conf.':<12} {'Filtrováno':<11} {'Accuracy':<10} {'Orig. Acc.':<10} {'Avg Conf.':<10}\n")
        f.write("-" * 120 + "\n")

        for stats in valid_stats:
            f.write(f"{stats['filename']:<20} {stats['total_records']:<8} {stats['annotated_records']:<8} "
                   f"{stats['high_confidence_annotated']:<12} {stats['filtered_out_count']:<11} "
                   f"{stats['accuracy']:<10.3f} {stats['original_accuracy']:<10.3f} {stats['avg_confidence_all']:<10.3f}\n")

        f.write("-" * 120 + "\n")
        f.write(f"{'CELKEM':<20} {sum(s['total_records'] for s in valid_stats):<8} {total_annotated:<8} "
               f"{total_high_confidence:<12} {total_filtered_out:<11} "
               f"{overall_accuracy:<10.3f} {overall_original_accuracy:<10.3f} {overall_avg_confidence:<10.3f}\n\n")

        f.write("SOUHRNNÉ STATISTIKY SPOLEHLIVOSTI:\n")
        f.write("-" * 80 + "\n")
        f.write(f"Celkem anotovaných záznamů: {total_annotated}\n")
        f.write(f"Záznamy s vysokou spolehlivostí: {total_high_confidence} ({total_high_confidence/total_annotated*100:.1f}%)\n")
        f.write(f"Odfiltrované záznamy: {total_filtered_out} ({total_filtered_out/total_annotated*100:.1f}%)\n")
        f.write(f"Průměrná spolehlivost: {overall_avg_confidence:.3f}\n")
        f.write(f"Accuracy po filtrování: {overall_accuracy:.3f}\n")
        f.write(f"Accuracy před filtrováním: {overall_original_accuracy:.3f}\n")
        f.write(f"Rozdíl v accuracy: {overall_accuracy - overall_original_accuracy:+.3f}\n\n")
        
        f.write("DETAILNÍ ANALÝZA PO TŘÍDÁCH:\n")
        f.write("-" * 110 + "\n")
        f.write(f"{'Třída':<8} {'Název':<30} {'Precision':<12} {'Recall':<12} {'F1-Score':<12} {'Support':<10} {'Avg Conf.':<10}\n")
        f.write("-" * 110 + "\n")

        for i in range(max_result_class):
            class_num = i + 1
            class_name = get_result_class_name(class_num)
            avg_conf = class_avg_confidences[i]
            f.write(f"{class_num:<8} {class_name:<30} {overall_precision[i]:<12.3f} {overall_recall[i]:<12.3f} "
                   f"{overall_f1[i]:<12.3f} {overall_support[i]:<10} {avg_conf:<10.3f}\n")

        f.write("-" * 110 + "\n")
        f.write(f"{'Macro avg':<8} {'(průměr všech tříd)':<30} {macro_prec:<12.3f} {macro_rec:<12.3f} {macro_f1:<12.3f} {sum(overall_support):<10} {overall_classified_confidence:<10.3f}\n")
        f.write(f"{'Weighted avg':<8} {'(vážený průměr)':<30} {weighted_prec:<12.3f} {weighted_rec:<12.3f} {weighted_f1:<12.3f} {sum(overall_support):<10} {overall_classified_confidence:<10.3f}\n")
    
    # Vytvoření grafů
    create_visualizations(valid_stats, all_true_labels, all_pred_labels, max_result_class, output_dir)

    # Vytvoření grafů spolehlivosti
    create_confidence_visualizations(valid_stats, all_confidences, confidence_threshold, output_dir)

    # Analýza nejčastějších chyb
    create_error_analysis(all_true_labels, all_pred_labels, max_result_class, output_dir)

    # Export predikovaných výsledků
    if processed_files:
        export_predictions(processed_files, output_dir)
    
    return {
        'overall_accuracy': overall_accuracy,
        'overall_original_accuracy': overall_original_accuracy,
        'macro_f1': macro_f1,
        'weighted_f1': weighted_f1,
        'total_files': len(valid_stats),
        'total_annotated': total_annotated,
        'total_high_confidence': total_high_confidence,
        'total_filtered_out': total_filtered_out,
        'overall_avg_confidence': overall_avg_confidence,
        'confidence_threshold': confidence_threshold
    }

def create_confidence_visualizations(valid_stats, all_confidences, confidence_threshold, output_dir):
    """Vytvoří vizualizace pro analýzu spolehlivosti"""

    # 1. Histogram spolehlivosti
    plt.figure(figsize=(12, 6))
    plt.hist(all_confidences, bins=50, alpha=0.7, edgecolor='black')
    plt.axvline(confidence_threshold, color='red', linestyle='--', linewidth=2,
                label=f'Práh spolehlivosti ({confidence_threshold})')
    plt.xlabel('Spolehlivost (Confidence)')
    plt.ylabel('Počet záznamů')
    plt.title('Distribuce spolehlivosti klasifikace')
    plt.legend()
    plt.grid(True, alpha=0.3)
    plt.tight_layout()
    plt.savefig(os.path.join(output_dir, 'confidence_distribution.png'), dpi=300, bbox_inches='tight')
    plt.close()

    # 2. Graf spolehlivosti pro jednotlivé soubory
    plt.figure(figsize=(15, 6))
    filenames = [s['filename'] for s in valid_stats]
    avg_confidences = [s['avg_confidence_all'] for s in valid_stats]
    high_conf_ratios = [s['high_confidence_annotated'] / s['annotated_records'] * 100
                       for s in valid_stats]

    x = range(len(filenames))
    width = 0.35

    fig, ax1 = plt.subplots(figsize=(15, 6))

    # Průměrná spolehlivost
    bars1 = ax1.bar([i - width/2 for i in x], avg_confidences, width,
                    label='Průměrná spolehlivost', alpha=0.7)
    ax1.set_xlabel('Soubory')
    ax1.set_ylabel('Průměrná spolehlivost', color='blue')
    ax1.tick_params(axis='y', labelcolor='blue')
    ax1.set_ylim(0, 1)

    # Druhá osa pro procenta
    ax2 = ax1.twinx()
    bars2 = ax2.bar([i + width/2 for i in x], high_conf_ratios, width,
                    label='% vysoká spolehlivost', alpha=0.7, color='orange')
    ax2.set_ylabel('% záznamů s vysokou spolehlivostí', color='orange')
    ax2.tick_params(axis='y', labelcolor='orange')
    ax2.set_ylim(0, 100)

    # Nastavení os
    ax1.set_xticks(x)
    ax1.set_xticklabels(filenames, rotation=45, ha='right')
    ax1.axhline(confidence_threshold, color='red', linestyle='--', alpha=0.7)

    # Legenda
    lines1, labels1 = ax1.get_legend_handles_labels()
    lines2, labels2 = ax2.get_legend_handles_labels()
    ax1.legend(lines1 + lines2, labels1 + labels2, loc='upper left')

    plt.title('Analýza spolehlivosti pro jednotlivé soubory')
    plt.tight_layout()
    plt.savefig(os.path.join(output_dir, 'confidence_per_file.png'), dpi=300, bbox_inches='tight')
    plt.close()

def create_error_analysis(all_true_labels, all_pred_labels, max_result_class, output_dir):
    """Vytvoří analýzu nejčastějších chyb klasifikace"""
    conf_matrix = confusion_matrix(all_true_labels, all_pred_labels, labels=range(1, max_result_class + 1))

    # Najdeme nejčastější chyby (mimo diagonálu)
    errors = []
    for i in range(max_result_class):
        for j in range(max_result_class):
            if i != j and conf_matrix[i, j] > 0:
                true_class = i + 1
                pred_class = j + 1
                count = conf_matrix[i, j]
                errors.append({
                    'true_class': true_class,
                    'true_class_name': get_result_class_name(true_class),
                    'pred_class': pred_class,
                    'pred_class_name': get_result_class_name(pred_class),
                    'count': count
                })

    # Seřadíme podle počtu chyb
    errors.sort(key=lambda x: x['count'], reverse=True)

    # Uložíme do CSV
    if errors:
        error_df = pd.DataFrame(errors)
        error_df.to_csv(os.path.join(output_dir, 'classification_errors.csv'), index=False)

        # Vytvoříme textový report s nejčastějšími chybami
        with open(os.path.join(output_dir, 'error_analysis.txt'), 'w', encoding='utf-8') as f:
            f.write("ANALÝZA NEJČASTĚJŠÍCH CHYB KLASIFIKACE\n")
            f.write("=" * 80 + "\n\n")
            f.write("Top 10 nejčastějších záměn:\n")
            f.write("-" * 80 + "\n")
            f.write(f"{'Skutečná třída':<25} {'Predikovaná třída':<25} {'Počet':<10}\n")
            f.write("-" * 80 + "\n")

            for error in errors[:10]:
                f.write(f"{error['true_class_name']:<25} {error['pred_class_name']:<25} {error['count']:<10}\n")

        print(f"\nNejčastější chyby klasifikace:")
        print("-" * 80)
        print(f"{'Skutečná třída':<25} {'Predikovaná třída':<25} {'Počet':<10}")
        print("-" * 80)
        for error in errors[:5]:  # Zobrazíme jen top 5 v konzoli
            print(f"{error['true_class_name']:<25} {error['pred_class_name']:<25} {error['count']:<10}")

def export_predictions(processed_files, output_dir):
    """Exportuje soubory s predikcemi do podsložky"""
    predictions_dir = os.path.join(output_dir, 'predictions')
    os.makedirs(predictions_dir, exist_ok=True)

    for file_path, df_with_predictions in processed_files:
        if df_with_predictions is not None:
            filename = os.path.basename(file_path)
            output_path = os.path.join(predictions_dir, f"predicted_{filename}")
            df_with_predictions.to_csv(output_path, index=False)

def create_visualizations(valid_stats, all_true_labels, all_pred_labels, max_result_class, output_dir):
    """Vytvoří vizualizace výsledků"""
    
    # 1. Graf přesnosti pro jednotlivé soubory
    plt.figure(figsize=(15, 6))
    filenames = [s['filename'] for s in valid_stats]
    accuracies = [s['accuracy'] for s in valid_stats]
    
    plt.bar(range(len(filenames)), accuracies)
    plt.xlabel('Soubory')
    plt.ylabel('Přesnost (Accuracy)')
    plt.title('Přesnost klasifikace pro jednotlivé soubory')
    plt.xticks(range(len(filenames)), filenames, rotation=45, ha='right')
    plt.ylim(0, 1)
    plt.grid(True, alpha=0.3)
    plt.tight_layout()
    plt.savefig(os.path.join(output_dir, 'accuracy_per_file.png'), dpi=300, bbox_inches='tight')
    plt.close()
    
    # 2. Matice záměn
    overall_conf_matrix = confusion_matrix(all_true_labels, all_pred_labels, labels=range(1, max_result_class + 1))

    plt.figure(figsize=(16, 14))
    class_labels = [f"{i}\n{get_result_class_name(i)}" for i in range(1, max_result_class + 1)]

    try:
        import seaborn as sns
        sns.heatmap(overall_conf_matrix, annot=True, fmt='d', cmap='Blues',
                    xticklabels=class_labels,
                    yticklabels=class_labels)
    except ImportError:
        # Fallback bez seaborn
        plt.imshow(overall_conf_matrix, cmap='Blues', interpolation='nearest')
        plt.colorbar()
        for i in range(len(class_labels)):
            for j in range(len(class_labels)):
                plt.text(j, i, str(overall_conf_matrix[i, j]), ha='center', va='center')
        plt.xticks(range(len(class_labels)), class_labels, rotation=45, ha='right')
        plt.yticks(range(len(class_labels)), class_labels, rotation=0)
    plt.xlabel('Predikovaná třída')
    plt.ylabel('Skutečná třída')
    plt.title('Matice záměn - celkové výsledky')
    plt.xticks(rotation=45, ha='right')
    plt.yticks(rotation=0)
    plt.tight_layout()
    plt.savefig(os.path.join(output_dir, 'confusion_matrix.png'), dpi=300, bbox_inches='tight')
    plt.close()
    
    # 3. F1-score pro jednotlivé třídy
    overall_precision, overall_recall, overall_f1, overall_support = precision_recall_fscore_support(
        all_true_labels, all_pred_labels, average=None, labels=range(1, max_result_class + 1), zero_division=0
    )

    plt.figure(figsize=(16, 8))
    classes = range(1, max_result_class + 1)
    class_names = [get_result_class_name(i) for i in classes]

    bars = plt.bar(classes, overall_f1)
    plt.xlabel('Třída')
    plt.ylabel('F1-Score')
    plt.title('F1-Score pro jednotlivé třídy')
    plt.xticks(classes, [f"{i}\n{name}" for i, name in zip(classes, class_names)], rotation=45, ha='right')
    plt.ylim(0, 1)
    plt.grid(True, alpha=0.3)

    # Přidání hodnot nad sloupce
    for bar, f1_val in zip(bars, overall_f1):
        if f1_val > 0:
            plt.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 0.01,
                    f'{f1_val:.3f}', ha='center', va='bottom', fontsize=8)

    plt.tight_layout()
    plt.savefig(os.path.join(output_dir, 'f1_score_per_class.png'), dpi=300, bbox_inches='tight')
    plt.close()

if __name__ == '__main__':
    device = CONFIG['device']
    print(f"Používané zařízení: {device}")
    
    # Maximální hodnoty tříd z datasetu
    MAX_KEY_CLASS = 26
    MAX_VALUE_CLASS = 8
    MAX_RESULT_CLASS = 18
    
    # Inicializace modelu
    node_in_dim = calculate_node_dim(MAX_KEY_CLASS, MAX_VALUE_CLASS)
    model = InterpretableGATClassifier(
        node_in_dim=node_in_dim,
        output_dim=MAX_RESULT_CLASS,  # 18 výstupních tříd
        num_value_classes=MAX_VALUE_CLASS + 1  # 9 tříd (0-8)
    ).to(device)
    
    # Načtení natrénovaného modelu
    try:
        model.load_state_dict(torch.load(CONFIG['model_path'], map_location=device))
        print(f"Model úspěšně načten z: {CONFIG['model_path']}")
    except FileNotFoundError:
        print(f"CHYBA: Model nenalezen na cestě: {CONFIG['model_path']}")
        print("Ujistěte se, že máte natrénovaný model.")
        exit(1)
    
    # Načtení všech souborů ze složky Results
    result_files = load_files_from_results(CONFIG['results_dir'])
    print(f"Nalezeno {len(result_files)} souborů k vyhodnocení")
    
    if not result_files:
        print(f"CHYBA: Žádné CSV soubory nenalezeny ve složce: {CONFIG['results_dir']}")
        exit(1)



    # Vyhodnocení všech souborů
    all_stats = []
    processed_files = []
    
    print("\nZpracovávání souborů...")
    for i, file_path in enumerate(result_files, 1):
        print(f"[{i}/{len(result_files)}] Zpracovávám: {os.path.basename(file_path)}")
        
        try:
            stats, df_with_predictions = evaluate_single_file(
                model, file_path, device, MAX_KEY_CLASS, MAX_VALUE_CLASS, MAX_RESULT_CLASS
            )
            all_stats.append(stats)
            processed_files.append((file_path, df_with_predictions))
            
            if stats:
                print(f"  -> Anotované záznamy: {stats['annotated_records']}, Přesnost: {stats['accuracy']:.3f}")
            else:
                print(f"  -> Žádné anotované záznamy")
                
        except Exception as e:
            print(f"  -> CHYBA při zpracování: {e}")
            all_stats.append(None)
    
    # Vytvoření detailních statistik
    print(f"\nVytváření detailních statistik...")
    summary = create_detailed_statistics(all_stats, MAX_RESULT_CLASS, CONFIG['output_dir'], processed_files)
    
    if summary:
        print(f"\nSOUHRN:")
        print(f"Práh spolehlivosti: {summary['confidence_threshold']}")
        print(f"Celková přesnost (po filtrování): {summary['overall_accuracy']:.3f}")
        print(f"Celková přesnost (před filtrováním): {summary['overall_original_accuracy']:.3f}")
        print(f"Rozdíl v přesnosti: {summary['overall_accuracy'] - summary['overall_original_accuracy']:+.3f}")
        print(f"Macro F1-Score: {summary['macro_f1']:.3f}")
        print(f"Weighted F1-Score: {summary['weighted_f1']:.3f}")
        print(f"Zpracováno souborů: {summary['total_files']}")
        print(f"Celkem anotovaných záznamů: {summary['total_annotated']}")
        print(f"Záznamy s vysokou spolehlivostí: {summary['total_high_confidence']} ({summary['total_high_confidence']/summary['total_annotated']*100:.1f}%)")
        print(f"Odfiltrované záznamy: {summary['total_filtered_out']} ({summary['total_filtered_out']/summary['total_annotated']*100:.1f}%)")
        print(f"Průměrná spolehlivost: {summary['overall_avg_confidence']:.3f}")
        print(f"\nVýsledky uloženy do složky: {CONFIG['output_dir']}")
    else:
        print("Nepodařilo se vytvořit statistiky - žádná platná data.")
