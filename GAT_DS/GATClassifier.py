import networkx as nx
import numpy as np
import pandas as pd
import torch
import torch.nn as nn
import torch.nn.functional as F
import matplotlib.pyplot as plt
import os
from sklearn.metrics import f1_score

from utils import utils


class GATLayer(nn.Module):
    """
    GAT vrstva s podporou edge features
    """

    def __init__(self, node_in_dim, edge_in_dim, node_out_dim, heads=4, dropout_rate=0.3):
        super().__init__()
        self.heads = heads
        self.node_out_dim = node_out_dim

        # Lineární projekce
        self.linear_nodes = nn.Linear(node_in_dim, node_out_dim * heads, bias=False)
        self.linear_edges = nn.Linear(edge_in_dim, heads, bias=False)

        # Parametry pozornosti
        self.attn_l = nn.Parameter(torch.Tensor(1, heads, node_out_dim))
        self.attn_r = nn.Parameter(torch.Tensor(1, heads, node_out_dim))

        self.dropout = nn.Dropout(p=dropout_rate)
        self.reset_parameters()

    def reset_parameters(self):
        nn.init.xavier_uniform_(self.linear_nodes.weight)
        nn.init.xavier_uniform_(self.linear_edges.weight)
        nn.init.xavier_uniform_(self.attn_l)
        nn.init.xavier_uniform_(self.attn_r)

    def forward(self, node_features, edge_features, adj):
        B, N, _ = node_features.shape

        # 1. Lineární projekce
        Wh = self.linear_nodes(node_features).view(B, N, self.heads, self.node_out_dim)
        We = self.linear_edges(edge_features)  # [B, N, N, heads]

        # 2. Výpočet skóre pozornosti
        score_l = (Wh * self.attn_l).sum(dim=-1).unsqueeze(2)  # [B, N, 1, heads]
        score_r = (Wh * self.attn_r).sum(dim=-1).unsqueeze(1)  # [B, 1, N, heads]

        scores = score_l + score_r + We
        scores = F.leaky_relu(scores, negative_slope=0.2)

        # 3. Maskování a Softmax
        mask = (adj == 0).unsqueeze(-1)
        scores = scores.masked_fill(mask, -1e9)
        attn = torch.softmax(scores, dim=2)
        attn = self.dropout(attn)

        # Uložení pozornostních vah před dropoutem
        self.attention_weights = attn.clone().detach()

        # 4. Agregace
        attn_permuted = attn.permute(0, 3, 1, 2)
        Wh_permuted = Wh.permute(0, 2, 1, 3)
        node_repr = torch.matmul(attn_permuted, Wh_permuted)
        node_repr = node_repr.permute(0, 2, 1, 3).contiguous().view(B, N, -1)

        return node_repr

class GatNodeClassifier(nn.Module):
    """
    Kompletní GAT klasifikátor uzlů
    """

    def __init__(self, node_in_dim, hidden_dim=128, heads=16, output_dim=18, num_value_classes=9, num_layers=2, dropout_rate=0.5):
        super().__init__()
        self.layers = nn.ModuleList()
        self.dropout = nn.Dropout(dropout_rate)

        # První vrstva
        self.layers.append(GATLayer(
            node_in_dim,
            7,  # dx, dy, dist, angle, priority_hint, page_hint, type_affinity_hint
            hidden_dim,
            heads,
            dropout_rate
        ))

        self.valueclass_embed = nn.Linear(num_value_classes, node_in_dim)

        # Skryté vrstvy
        for _ in range(1, num_layers):
            self.layers.append(GATLayer(
                hidden_dim * heads,
                7,  # dx, dy, dist, angle, priority_hint, page_hint, type_affinity_hint
                hidden_dim,
                heads,
                dropout_rate
            ))

        # Výstupní vrstva
        self.classifier = nn.Linear(hidden_dim * heads, output_dim)

    def forward(self, node_features, edge_features, adj, value_class_tensor):

        # Vytvoř embed z value_class
        value_embed = self.valueclass_embed(value_class_tensor)  # [B, N, D]

        # Kombinuj s původními node_features
        h = node_features + value_embed  # nebo torch.cat([...], dim=-1)

        for i, layer in enumerate(self.layers):
            h = layer(h, edge_features, adj)
            if i < len(self.layers) - 1:
                h = F.elu(h)
                h = self.dropout(h)

        return self.classifier(h)


class InterpretableGATClassifier(GatNodeClassifier):
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        # Nahradíme vrstvy interpretovatelnými verzemi
        for i in range(len(self.layers)):
            self.layers[i] = GATLayer(
                self.layers[i].linear_nodes.in_features,
                7,  # dx, dy, dist, angle, priority_hint, page_hint, type_affinity_hint
                self.layers[i].node_out_dim,
                self.layers[i].heads,
                self.layers[i].dropout.p
            )

def normalize_data(df, max_key_class, max_value_class):
    """
    Normalizuje data a přidává geometrické vlastnosti
    """
    # Kontrola prvního řádku (stránka)
    if df.iloc[0]['text'] != 'page' or df.iloc[0]['left'] != 0 or df.iloc[0]['top'] != 0:
        raise ValueError("První řádek musí být 'page' s left=0, top=0")

    page_width = df.iloc[0]['width']
    page_height = df.iloc[0]['height']

    # Výpočet středů
    df['cx'] = df['left'] + df['width'] / 2
    df['cy'] = df['top'] + df['height'] / 2

    # Normalizace souřadnic
    df['left_norm'] = df['left'] / page_width
    df['top_norm'] = df['top'] / page_height
    df['width_norm'] = df['width'] / page_width
    df['height_norm'] = df['height'] / page_height
    df['cx_norm'] = df['cx'] / page_width
    df['cy_norm'] = df['cy'] / page_height

    # One-hot encoding pro key_class a value_class
    def one_hot_encode(df, col_name, max_classes, prefix):
        for i in range(max_classes + 1):  # včetně 0
            df[f'{prefix}_{i}'] = (df[col_name] == i).astype(int)
        return df

    df = one_hot_encode(df, 'key_class', max_key_class, 'key_class')
    df = one_hot_encode(df, 'value_class', max_value_class, 'value_class')

    return df


def calculate_node_dim(max_key_class, max_value_class):
    """
    Vypočítá dimenzi vstupních vlastností uzlů
    """
    # 6 geometrických vlastností + one-hot pro key_class a value_class
    return 6 + (max_key_class + 1) + (max_value_class + 1)



def prepare_graph_data(df, device, max_key_class, max_value_class, max_result_class, is_training=True):
    """
    Připraví grafová data pro model s typovou maticí pro omezení klasifikací
    """
    # Normalizace a přidání vlastností
    df = normalize_data(df, max_key_class, max_value_class)

    # Vytvoření typové matice
    type_affinity_matrix = create_type_affinity_matrix(max_value_class, max_result_class)

    # Seznam sloupců pro vlastnosti uzlů
    coord_cols = ['left_norm', 'top_norm', 'width_norm', 'height_norm', 'cx_norm', 'cy_norm']
    key_cols = [col for col in df.columns if col.startswith('key_class_')]
    value_cols = [col for col in df.columns if col.startswith('value_class_')]
    node_feature_cols = coord_cols + key_cols + value_cols

    node_features_np = df[node_feature_cols].values.astype(np.float32)
    num_nodes = node_features_np.shape[0]

    # One-hot encoding pro value_class
    value_class_ids = df['value_class'].values.astype(np.int64)
    num_value_classes = max_value_class + 1

    value_class_onehot = np.zeros((num_nodes, num_value_classes), dtype=np.float32)
    value_class_onehot[np.arange(num_nodes), value_class_ids] = 1.0

    # Převedeme na torch tensor
    value_class_tensor = torch.tensor(value_class_onehot).unsqueeze(0).to(device)

    # Výpočet vlastností hran
    cx_norm = df['cx_norm'].values
    cy_norm = df['cy_norm'].values

    dx = cx_norm[:, np.newaxis] - cx_norm[np.newaxis, :]
    dy = cy_norm[:, np.newaxis] - cy_norm[np.newaxis, :]
    dist = np.sqrt(dx ** 2 + dy ** 2 + 1e-8)  # +epsilon pro stabilitu
    angle = np.arctan2(dy, dx)

    page_mask = df['text'] == 'page'
    page_indices = np.where(page_mask)[0]  # třeba [0] pokud je jen jeden

    epsilon = 1e-3
    page_hint = np.full_like(dx, fill_value=epsilon, dtype=np.float32)
    is_key = (df['key_class'] > 0).values
    is_value = (df['value_class'] > 0).values
    for i in page_indices:
        for j in range(num_nodes):
            if is_key[j]:
                page_hint[i, j] = 0.7
                page_hint[j, i] = 0.7
            elif is_value[j]:
                page_hint[i, j] = 1.0
                page_hint[j, i] = 1.0

    priority_hint = np.full_like(dx, fill_value=epsilon, dtype=np.float32)
    max_distance = 0.8  # Maximum distance to consider (normalized 0-1)
    min_priority = 0.1  # Minimum priority value at max_distance

    # Předpočítané direction weights
    dir_weights = np.ones_like(dx, dtype=np.float32) * 0.2  # Default = above
    mask_right = (dx > 0) & (np.abs(dy) < 0.1)
    mask_below = dy > 0
    mask_left = (dx < 0) & (np.abs(dy) < 0.1)

    dir_weights[mask_right] = 1.0
    dir_weights[mask_below] = 0.9
    dir_weights[mask_left] = 0.3

    # Maska key->value párů
    mask_kv = np.outer(is_key, is_value)

    # Distance weight: linear drop-off from 1.0 to min_priority
    dist_weight = np.clip(1.0 - (1.0 - min_priority) * (dist / max_distance), min_priority, 1.0)

    # Spojení masek a výpočet
    priority_hint[mask_kv] = dir_weights[mask_kv] * dist_weight[mask_kv]
    priority_hint[~mask_kv & (dist > max_distance)] = epsilon

    # Příprava labelů pro typovou afinitu
    if 'result_class' in df.columns:
        labels_np = df['result_class'].values.astype(np.int64)
    else:
        labels_np = np.zeros(num_nodes, dtype=np.int64)

    # Typová afinita - omezuje možné klasifikace na základě typu klíče a hodnoty
    type_affinity_hint = np.zeros_like(dx, dtype=np.float32)
    key_classes = df['key_class'].values
    value_classes = df['value_class'].values

    for i in range(num_nodes):
        for j in range(num_nodes):
            if is_key[i] and is_value[j]:
                key_class = key_classes[i]
                value_class = value_classes[j]
                result_class = labels_np[i]  # Use the label as the result class
                # Use the type affinity matrix to determine allowed relationships
                if (value_class <= max_value_class and 
                    result_class <= max_result_class and  
                    type_affinity_matrix[value_class, result_class] > 0):
                    type_affinity_hint[i, j] = 1.0

    edge_features_np = np.stack([dx, dy, dist, angle, priority_hint], axis=-1).astype(np.float32)
    edge_features_np = np.concatenate(
        [edge_features_np, page_hint[..., np.newaxis], type_affinity_hint[..., np.newaxis]],
        axis=-1
    ).astype(np.float32)

    # Matice sousednosti (plně propojená kromě self-loop)
    adj_np = np.ones((num_nodes, num_nodes), dtype=np.float32)
    np.fill_diagonal(adj_np, 0)

    # Příprava masky
    if is_training:
        # Trénovací maska: pouze hodnotové uzly s platnou třídou (>0)
        train_mask_np = (df['value_class'] > 0) & (labels_np > 0)
    else:
        # Pro inferenci: všechny hodnotové uzly
        train_mask_np = (df['value_class'] > 0)

    # Konverze na tenzory
    node_features = torch.tensor(node_features_np).unsqueeze(0).to(device)
    edge_features = torch.tensor(edge_features_np).unsqueeze(0).to(device)
    adj = torch.tensor(adj_np).unsqueeze(0).to(device)
    labels = torch.tensor(labels_np).to(device)
    train_mask = torch.tensor(train_mask_np).to(device)

    return node_features, edge_features, adj, labels, train_mask, value_class_tensor


def visualize_attention(model, node_features, edge_features, adj, df, output_path="attention_visualization.png"):
    try:
        import seaborn as sns
    except ImportError:
        print("Varování: seaborn není nainstalován, vizualizace nebude dostupná")
        return None

    # Předpověď s uložením attention vah
    with torch.no_grad():
        model(node_features, edge_features, adj)

    # Získání pozornostních vah z první vrstvy
    attn_weights = model.layers[0].attention_weights.squeeze(0).cpu().numpy()

    # Průměr přes hlavy
    mean_attn = np.mean(attn_weights, axis=-1)

    # Vytvoření grafu
    plt.figure(figsize=(30, 24))

    # Heatmapa pozornostních vah
    plt.subplot(2, 1, 1)
    sns.heatmap(
        mean_attn,
        annot=True,
        fmt=".2f",
        cmap="viridis",
        xticklabels=df['text'].str.wrap(15),
        yticklabels=df['text'].str.wrap(15)
    )
    plt.title("Attention Weights Heatmap")
    plt.xlabel("Target Nodes")
    plt.ylabel("Source Nodes")
    plt.tight_layout()

    # Síťový graf s vizualizací vah
    plt.subplot(2, 1, 2)
    G = nx.DiGraph()

    # Přidání uzlů s pozicemi
    positions = {}
    for i, row in df.iterrows():
        G.add_node(i, label=row['text'])
        positions[i] = (row['cx_norm'], -row['cy_norm'])  # Otočení Y osy

    # Přidání hran s vahami
    threshold = 0.1  # Zobraz pouze významné vztahy
    for i in range(len(df)):
        for j in range(len(df)):
            if mean_attn[i, j] > threshold and i != j:
                G.add_edge(i, j, weight=mean_attn[i, j])

    # Vykreslení
    edge_weights = [G[u][v]['weight'] * 5 for u, v in G.edges()]

    nx.draw_networkx_nodes(
        G, positions, node_size=500,
        node_color="lightblue", alpha=0.9
    )

    nx.draw_networkx_edges(
        G, positions, edge_color="gray",
        width=edge_weights, alpha=0.6,
        arrowstyle='-|>', arrowsize=15
    )

    nx.draw_networkx_labels(
        G, positions,
        labels={i: df.iloc[i]['text'] for i in range(len(df))},
        font_size=8
    )

    plt.title("Document Graph with Attention Weights")
    plt.axis('off')
    plt.tight_layout()

    # Uložení
    plt.savefig(output_path, dpi=300)
    plt.close()

    return mean_attn


def visualize_class_attention(attention_matrix, df, class_type='key_class'):
    try:
        import seaborn as sns
    except ImportError:
        print("Varování: seaborn není nainstalován, vizualizace nebude dostupná")
        return None

    plt.figure(figsize=(24, 20))

    # Agregace attention podle tříd
    class_attention = {}
    classes = sorted(df[class_type].unique())

    # Pro každou třídu spočítáme průměrnou pozornost
    for cls in classes:
        # Indexy uzlů patřících do této třídy
        class_indices = df.index[df[class_type] == cls].tolist()

        if not class_indices:
            continue

        # Průměrná pozornost K této třídě (sloupcový průměr)
        attn_to_class = attention_matrix[:, class_indices].mean(axis=1)

        # Průměrná pozornost OD této třídy (řádkový průměr)
        attn_from_class = attention_matrix[class_indices, :].mean(axis=0)

        # Celková relevance třídy
        class_attention[cls] = (attn_to_class + attn_from_class) / 2

    # Převod na matici pro heatmapu
    # Řádky = třídy, Sloupce = uzly
    attn_matrix = np.array([class_attention[cls] for cls in classes])

    # Heatmapa
    sns.heatmap(
        attn_matrix,
        cmap="viridis",
        xticklabels=df['text'].str.wrap(15),
        yticklabels=[f"Class {c}" for c in classes],
        annot=True,
        fmt=".2f",
        linewidths=0.5
    )

    plt.title(f"Attention by {class_type}")
    plt.xlabel("Target Nodes")
    plt.ylabel("Source Classes")
    plt.tight_layout()
    plt.savefig(f"class_{class_type}_attention.png", dpi=300)
    plt.close()

    return attn_matrix

def plot_3d_attention(attention_matrix, df):
    fig = plt.figure(figsize=(32, 24))
    ax = fig.add_subplot(111, projection='3d')

    # Pozice uzlů
    x = df['cx_norm']
    y = df['cy_norm']
    z = np.zeros(len(df))  # Můžeme přidat třetí rozměr podle potřeby

    # Velikost bodů podle důležitosti
    node_importance = attention_matrix.sum(axis=1)
    sizes = 500 + 2000 * (node_importance / node_importance.max())

    # Vykreslení uzlů
    ax.scatter(x, y, z, s=sizes, c='blue', alpha=0.7)

    # Vykreslení hran
    for i in range(len(df)):
        for j in range(len(df)):
            if attention_matrix[i, j] > 0.15 and i != j:
                ax.plot(
                    [x[i], x[j]],
                    [y[i], y[j]],
                    [z[i], z[j]],
                    linewidth=attention_matrix[i, j] * 10,
                    color='gray',
                    alpha=0.5
                )

    # Popisky
    for i, row in df.iterrows():
        ax.text(x[i], y[i], z[i], row['text'], fontsize=8)

    ax.set_xlabel('X Position')
    ax.set_ylabel('Y Position')
    ax.set_zlabel('Layer Depth')
    plt.title("3D Attention Visualization")
    plt.savefig("3d_attention.png", dpi=300)
    plt.close()


def compute_soft_affinity_loss(predictions, targets, value_classes, affinity_matrix):
    """
    Váhuje loss podle afinitní matice - povolené kombinace mají normální loss,
    zakázané kombinace mají vyšší penalizaci.
    """
    # Standard cross-entropy
    base_loss = F.cross_entropy(predictions.view(-1, predictions.size(-1)),
                                targets.view(-1), reduction='none')

    # Vytvoří váhy na základě afinitní matice
    flat_value_classes = value_classes.view(-1)
    flat_targets = targets.view(-1)

    # Affinity weights: 1.0 pro povolené, >1.0 pro zakázané
    affinity_weights = torch.where(
        affinity_matrix[flat_value_classes, flat_targets] > 0,
        torch.tensor(1.0),  # Normální loss pro povolené
        torch.tensor(10.0)  # 10x vyšší loss pro zakázané
    )

    weighted_loss = base_loss * affinity_weights
    return weighted_loss.mean()

def train_model(model, train_files, val_files, device, max_key_class, max_value_class, max_result_class,
                epochs=100, lr=1e-4, patience=5, output_dir="training_results"):
    """
    Rozšířená tréninková smyčka s vizualizací a early stopping
    """
    # Vytvoření výstupního adresáře
    os.makedirs(output_dir, exist_ok=True)

    #optimizer = torch.optim.Adam(model.parameters(), lr=lr)
    optimizer = torch.optim.Adam(model.parameters(),
                                lr=lr,
                                weight_decay=1e-4)  # L2 regularizace
    criterion = nn.CrossEntropyLoss()

    # Pro ukládání historie
    train_losses = []
    val_losses = []
    val_f1_scores = []
    best_f1 = 0.0
    epochs_no_improve = 0

    for epoch in range(epochs):
        # Tréninková fáze
        model.train()
        epoch_train_loss = 0.0
        train_batches = 0

        for file in train_files:
            df = pd.read_csv(file)
            node_feats, edge_feats, adj, labels, train_mask, value_class_tensor = prepare_graph_data(
                df, device, max_key_class, max_value_class, max_result_class, is_training=True
            )

            # Forward pass
            optimizer.zero_grad()
            #logits = model(node_feats, edge_feats, adj).squeeze(0)
            logits = model(node_feats, edge_feats, adj, value_class_tensor).squeeze(0)

            if train_mask.sum() > 0:
                loss = criterion(logits[train_mask], labels[train_mask] - 1)  # Mapování 1-18 -> 0-17
                loss.backward()
                optimizer.step()

                epoch_train_loss += loss.item()
                train_batches += 1

        # Průměrná tréninková loss pro epochu
        avg_train_loss = epoch_train_loss / train_batches if train_batches > 0 else 0
        train_losses.append(avg_train_loss)

        # Validační fáze
        model.eval()
        epoch_val_loss = 0.0
        all_preds = []
        all_labels = []
        val_batches = 0

        for file in val_files:
            df = pd.read_csv(file)
            node_feats, edge_feats, adj, labels, val_mask, value_class_tensor = prepare_graph_data(
                df, device, max_key_class, max_value_class, max_result_class, is_training=True
            )

            with torch.no_grad():
                #logits = model(node_feats, edge_feats, adj).squeeze(0)
                logits = model(node_feats, edge_feats, adj, value_class_tensor).squeeze(0)

                if val_mask.sum() > 0:
                    loss = criterion(logits[val_mask], labels[val_mask] - 1)
                    epoch_val_loss += loss.item()

                    # Predikce pro metriky
                    preds = torch.argmax(logits[val_mask], dim=1)
                    all_preds.extend(preds.cpu().numpy())
                    all_labels.extend((labels[val_mask] - 1).cpu().numpy())  # Mapování 1-18 -> 0-17

                    val_batches += 1

        # Průměrná validační loss
        avg_val_loss = epoch_val_loss / val_batches if val_batches > 0 else 0
        val_losses.append(avg_val_loss)

        # Výpočet F1 skóre
        if all_preds and all_labels:
            f1 = f1_score(all_labels, all_preds, average='weighted')
            val_f1_scores.append(f1)

            # Uložení nejlepšího modelu
            if f1 > best_f1:
                best_f1 = f1
                torch.save(model.state_dict(), os.path.join(output_dir, 'best_model.pth'))
                epochs_no_improve = 0
            else:
                epochs_no_improve += 1
        else:
            f1 = 0.0
            val_f1_scores.append(f1)

        # Výpis statistik
        print(f'Epoch {epoch + 1}/{epochs}: '
              f'Train Loss: {avg_train_loss:.4f}, '
              f'Val Loss: {avg_val_loss:.4f}, '
              f'Val F1: {f1:.4f}')

        # Early stopping
        if epochs_no_improve >= patience:
            print(f'Early stopping after {epoch + 1} epochs, no improvement for {patience} epochs')
            break

    # Vizualizace průběhu učení
    plt.figure(figsize=(12, 5))

    # Graf loss
    plt.subplot(1, 2, 1)
    plt.plot(train_losses, label='Train Loss')
    plt.plot(val_losses, label='Val Loss')
    plt.xlabel('Epoch')
    plt.ylabel('Loss')
    plt.title('Training and Validation Loss')
    plt.legend()
    plt.grid(True)

    # Graf F1 skóre
    plt.subplot(1, 2, 2)
    plt.plot(val_f1_scores, label='Val F1', color='green')
    plt.xlabel('Epoch')
    plt.ylabel('F1 Score')
    plt.title('Validation F1 Score')
    plt.ylim([0, 1])
    plt.legend()
    plt.grid(True)

    # Uložení grafu
    plt.tight_layout()
    plt.savefig(os.path.join(output_dir, 'training_metrics.png'))
    plt.close()

    # Uložení finálního modelu
    torch.save(model.state_dict(), os.path.join(output_dir, 'final_model.pth'))

    print(f'Training complete. Best validation F1: {best_f1:.4f}')

    return train_losses, val_losses, val_f1_scores

def predict(model, df, device, max_key_class, max_value_class, max_result_class):
    """
    Predikce pro nový dokument s informací o spolehlivosti klasifikace
    Vrací:
        predicted_classes: pole s predikovanými třídami (0 pro neklasifikované)
        confidences: pole s confidence scores (0.0-1.0)
    """
    # Příprava dat
    node_feats, edge_feats, adj, _, pred_mask, value_class_tensor = prepare_graph_data(
        df, device, max_key_class, max_value_class, max_result_class, is_training=False
    )
    # Predikce
    model.eval()
    with torch.no_grad():
        #logits = model(node_feats, edge_feats, adj).squeeze(0)  # [N, output_dim]
        logits = model(node_feats, edge_feats, adj, value_class_tensor).squeeze(0)

        # Výpočet confidence
        probabilities = F.softmax(logits, dim=1)
        confidences, pred_indices = torch.max(probabilities, dim=1)
        pred_classes = pred_indices + 1  # Mapování 0-17 -> 1-18

    # Inicializace výsledků
    predicted_classes = np.zeros(len(df), dtype=int)
    confidence_scores = np.zeros(len(df), dtype=float)

    # Pouze pro hodnotové uzly
    mask_np = pred_mask.cpu().numpy()
    predicted_classes[mask_np] = pred_classes[pred_mask].cpu().numpy()
    confidence_scores[mask_np] = confidences[pred_mask].cpu().numpy()

    return predicted_classes, confidence_scores

def predict_analyzed(model, df, device, max_key_class, max_value_class, max_result_class):
    """
    Predikce pro nový dokument s informací o spolehlivosti klasifikace
    Vrací:
        predicted_classes: pole s predikovanými třídami (0 pro neklasifikované)
        confidences: pole s confidence scores (0.0-1.0)
    """
    # Příprava dat
    node_feats, edge_feats, adj, _, pred_mask, value_class_tensor = prepare_graph_data(
        df, device, max_key_class, max_value_class, max_result_class, is_training=False
    )

    # Vizualizace
    attention_matrix = visualize_attention(
        model, node_feats, edge_feats, adj, df,
        output_path="document_attention.png"
    )

    # Vizualizace pozornostních vah mezi třídami
    visualize_class_attention(attention_matrix, df, class_type='key_class')
    #visualize_class_attention(attention_matrix, df, class_type='value_class')
    plot_3d_attention(attention_matrix, df)

    # Analýza pro konkrétní uzel
    target_node_idx = 10  # Index uzlu, který nás zajímá
    print(f"\nNejdůležitější vztahy pro uzel: '{df.iloc[target_node_idx]['text']}'")

    # Seřazení uzlů podle důležitosti
    sorted_indices = np.argsort(attention_matrix[target_node_idx])[::-1]

    for i in sorted_indices[:5]:  # Top 5 vztahů
        if i == target_node_idx:
            continue

        print(f"  -> {df.iloc[i]['text']}: {attention_matrix[target_node_idx, i]:.3f}")

    # Predikce
    model.eval()
    with torch.no_grad():
        logits = model(node_feats, edge_feats, adj, value_class_tensor).squeeze(0)  # [N, output_dim]

        # Výpočet confidence
        probabilities = F.softmax(logits, dim=1)
        confidences, pred_indices = torch.max(probabilities, dim=1)
        pred_classes = pred_indices + 1  # Mapování 0-17 -> 1-18

    # Inicializace výsledků
    predicted_classes = np.zeros(len(df), dtype=int)
    confidence_scores = np.zeros(len(df), dtype=float)

    # Pouze pro hodnotové uzly
    mask_np = pred_mask.cpu().numpy()
    predicted_classes[mask_np] = pred_classes[pred_mask].cpu().numpy()
    confidence_scores[mask_np] = confidences[pred_mask].cpu().numpy()

    return predicted_classes, confidence_scores


import os
import torch
import torch.nn as nn
import torch.nn.functional as F
import pandas as pd
import matplotlib.pyplot as plt
from sklearn.metrics import f1_score
import numpy as np


def create_type_affinity_matrix(max_value_class, max_result_class):
    """Vytvoří afinitní matici - stejná jako máte"""
    affinity_matrix = np.full((max_value_class + 1, max_result_class + 1), 1e-3, dtype=np.float32)
    #affinity_matrix[0, :] = 1.0  # Neznámý typ

    value_to_result_mapping = {
        1: [2, 3, 4],  # Datum
        2: [14],  # Procenta
        3: [14, 15, 16, 17, 18],  # Desetinné číslo
        4: [12],  # Číslo účtu
        5: [1, 5],  # Alfanumerický kód
        6: [11],  # IBAN
        7: [7, 9],  # DIČ
        8: [1, 5, 6, 8, 10, 14, 15, 16, 17, 18],  # Číslo (fallback)
    }

    for value_class, allowed_results in value_to_result_mapping.items():
        if value_class <= max_value_class:
            for result_class in allowed_results:
                if result_class <= max_result_class:
                    affinity_matrix[value_class, result_class] = 1.0

    return torch.tensor(affinity_matrix, dtype=torch.float32)


def compute_weighted_affinity_loss(logits, labels, value_classes, affinity_matrix, penalty_factor=5.0):
    """Option A: Weighted loss"""
    # logits už je 2D [num_masked_nodes, num_classes] po aplikaci train_mask
    # labels je 1D [num_masked_nodes] po aplikaci train_mask
    # value_classes je 1D [num_masked_nodes] po aplikaci train_mask

    device = logits.device
    affinity_matrix = affinity_matrix.to(device)

    # Mapování labels z 1-18 na 0-17 pro cross-entropy
    mapped_labels = labels - 1

    # Base cross-entropy loss
    base_loss = F.cross_entropy(logits, mapped_labels, reduction='none')

    # Affinity weights - použijeme původní labels (1-18) pro lookup v affinity matrix
    allowed_mask = affinity_matrix[value_classes, labels]  # labels zůstávají 1-18

    weights = torch.where(allowed_mask > 0,
                          torch.tensor(1.0, device=device),
                          torch.tensor(penalty_factor, device=device))

    weighted_loss = base_loss * weights
    return weighted_loss.mean()


def compute_auxiliary_affinity_loss(logits, labels, value_classes, affinity_matrix, lambda_affinity=0.5):
    """Option B: Auxiliary loss"""
    # logits: [num_masked_nodes, num_classes]
    # labels: [num_masked_nodes] (values 1-18)
    # value_classes: [num_masked_nodes] (values 0-8)

    device = logits.device
    affinity_matrix = affinity_matrix.to(device)

    # 1. Main loss - mapujeme labels z 1-18 na 0-17
    mapped_labels = labels - 1
    main_loss = F.cross_entropy(logits, mapped_labels)

    # 2. Auxiliary loss
    probs = F.softmax(logits, dim=-1)  # [num_masked_nodes, num_classes]

    # Ideal distribution podle affinity matrix
    # affinity_matrix[value_classes] má shape [num_masked_nodes, 19] (indexy 0-18)
    # ale potřebujeme jen indexy 1-18 (odpovídající logits classes 0-17)
    ideal_dist_full = affinity_matrix[value_classes]  # [num_masked_nodes, 19]
    ideal_distribution = ideal_dist_full[:, 1:]  # [num_masked_nodes, 18] - odřízneme index 0

    # Normalizace
    ideal_distribution = ideal_distribution / (ideal_distribution.sum(dim=-1, keepdim=True) + 1e-8)

    # KL divergence
    affinity_loss = F.kl_div(torch.log(probs + 1e-8), ideal_distribution, reduction='batchmean')

    total_loss = main_loss + lambda_affinity * affinity_loss
    return total_loss, main_loss, affinity_loss


# =============================================================================
# OPTION A: WEIGHTED LOSS INTEGRATION
# =============================================================================

def train_model_option_a(model, train_files, val_files, device, max_key_class, max_value_class, max_result_class,
                         epochs=100, lr=1e-4, patience=5, output_dir="training_results",
                         penalty_factor=5.0, use_affinity=True):
    """
    Tréninková funkce s Option A - Weighted Affinity Loss
    """
    os.makedirs(output_dir, exist_ok=True)

    # Vytvoř afinitní matici
    affinity_matrix = create_type_affinity_matrix(max_value_class, max_result_class) if use_affinity else None

    optimizer = torch.optim.Adam(model.parameters(), lr=lr, weight_decay=1e-4)
    criterion = nn.CrossEntropyLoss()  # Fallback pro případ bez affinity

    train_losses = []
    val_losses = []
    val_f1_scores = []
    best_f1 = 0.0
    epochs_no_improve = 0

    for epoch in range(epochs):
        # Tréninková fáze
        model.train()
        epoch_train_loss = 0.0
        train_batches = 0

        for file in train_files:
            df = pd.read_csv(file)
            node_feats, edge_feats, adj, labels, train_mask, value_class_tensor = prepare_graph_data(
                df, device, max_key_class, max_value_class, max_result_class, is_training=True
            )

            optimizer.zero_grad()
            logits = model(node_feats, edge_feats, adj, value_class_tensor).squeeze(0)

            if train_mask.sum() > 0:
                if use_affinity and affinity_matrix is not None:
                    # Option A: Weighted affinity loss
                    value_classes = df['value_class'].values
                    value_classes_tensor = torch.tensor(value_classes, device=device)[train_mask]
                    loss = compute_weighted_affinity_loss(
                        logits[train_mask],
                        labels[train_mask],
                        value_classes_tensor,
                        affinity_matrix,
                        penalty_factor
                    )
                else:
                    # Fallback: standard loss
                    loss = criterion(logits[train_mask], labels[train_mask] - 1)

                loss.backward()
                optimizer.step()
                epoch_train_loss += loss.item()
                train_batches += 1

        # Validační fáze - stejná jako původní
        avg_train_loss = epoch_train_loss / train_batches if train_batches > 0 else 0
        train_losses.append(avg_train_loss)

        # Validace (stejná logika jako v původní funkci)
        model.eval()
        epoch_val_loss = 0.0
        all_preds = []
        all_labels = []
        val_batches = 0

        for file in val_files:
            df = pd.read_csv(file)
            node_feats, edge_feats, adj, labels, val_mask, value_class_tensor = prepare_graph_data(
                df, device, max_key_class, max_value_class, max_result_class, is_training=True
            )

            with torch.no_grad():
                logits = model(node_feats, edge_feats, adj, value_class_tensor).squeeze(0)

                if val_mask.sum() > 0:
                    # Pro validaci použijeme standard loss pro konzistenci
                    loss = criterion(logits[val_mask], labels[val_mask] - 1)
                    epoch_val_loss += loss.item()

                    preds = torch.argmax(logits[val_mask], dim=1)
                    all_preds.extend(preds.cpu().numpy())
                    all_labels.extend((labels[val_mask] - 1).cpu().numpy())
                    val_batches += 1

        # Zbytek stejný jako původní...
        avg_val_loss = epoch_val_loss / val_batches if val_batches > 0 else 0
        val_losses.append(avg_val_loss)

        if all_preds and all_labels:
            f1 = f1_score(all_labels, all_preds, average='weighted')
            val_f1_scores.append(f1)

            if f1 > best_f1:
                best_f1 = f1
                torch.save(model.state_dict(), os.path.join(output_dir, 'best_model.pth'))
                epochs_no_improve = 0
            else:
                epochs_no_improve += 1
        else:
            f1 = 0.0
            val_f1_scores.append(f1)

        print(f'Epoch {epoch + 1}/{epochs}: '
              f'Train Loss: {avg_train_loss:.4f}, '
              f'Val Loss: {avg_val_loss:.4f}, '
              f'Val F1: {f1:.4f}')

        if epochs_no_improve >= patience:
            print(f'Early stopping after {epoch + 1} epochs')
            break

    # Stejná vizualizace jako původní...
    plt.figure(figsize=(12, 5))
    plt.subplot(1, 2, 1)
    plt.plot(train_losses, label='Train Loss')
    plt.plot(val_losses, label='Val Loss')
    plt.xlabel('Epoch')
    plt.ylabel('Loss')
    plt.title('Training and Validation Loss')
    plt.legend()
    plt.grid(True)

    plt.subplot(1, 2, 2)
    plt.plot(val_f1_scores, label='Val F1', color='green')
    plt.xlabel('Epoch')
    plt.ylabel('F1 Score')
    plt.title('Validation F1 Score')
    plt.ylim([0, 1])
    plt.legend()
    plt.grid(True)

    plt.tight_layout()
    plt.savefig(os.path.join(output_dir, 'training_metrics.png'))
    plt.close()

    torch.save(model.state_dict(), os.path.join(output_dir, 'final_model.pth'))
    print(f'Training complete. Best validation F1: {best_f1:.4f}')

    return train_losses, val_losses, val_f1_scores


# =============================================================================
# OPTION B: AUXILIARY LOSS INTEGRATION
# =============================================================================

def train_model_option_b(model, train_files, val_files, device, max_key_class, max_value_class, max_result_class,
                         epochs=100, lr=1e-4, patience=5, output_dir="training_results",
                         lambda_affinity=0.3, use_affinity=True):
    """
    Tréninková funkce s Option B - Auxiliary Affinity Loss
    """
    os.makedirs(output_dir, exist_ok=True)

    # Vytvoř afinitní matici
    affinity_matrix = create_type_affinity_matrix(max_value_class, max_result_class) if use_affinity else None

    optimizer = torch.optim.Adam(model.parameters(), lr=lr, weight_decay=1e-4)
    criterion = nn.CrossEntropyLoss()

    train_losses = []
    train_main_losses = []
    train_aux_losses = []
    val_losses = []
    val_f1_scores = []
    best_f1 = 0.0
    epochs_no_improve = 0

    for epoch in range(epochs):
        # Tréninková fáze
        model.train()
        epoch_train_loss = 0.0
        epoch_main_loss = 0.0
        epoch_aux_loss = 0.0
        train_batches = 0

        for file in train_files:
            df = pd.read_csv(file)
            node_feats, edge_feats, adj, labels, train_mask, value_class_tensor = prepare_graph_data(
                df, device, max_key_class, max_value_class, max_result_class, is_training=True
            )

            optimizer.zero_grad()
            logits = model(node_feats, edge_feats, adj, value_class_tensor).squeeze(0)

            if train_mask.sum() > 0:
                if use_affinity and affinity_matrix is not None:
                    # Option B: Auxiliary affinity loss
                    value_classes = df['value_class'].values
                    value_classes_tensor = torch.tensor(value_classes, device=device)[train_mask]
                    total_loss, main_loss, aux_loss = compute_auxiliary_affinity_loss(
                        logits[train_mask],
                        labels[train_mask],
                        value_classes_tensor,
                        affinity_matrix,
                        lambda_affinity
                    )

                    epoch_main_loss += main_loss.item()
                    epoch_aux_loss += aux_loss.item()
                    loss = total_loss
                else:
                    # Fallback: standard loss
                    loss = criterion(logits[train_mask], labels[train_mask] - 1)

                loss.backward()
                optimizer.step()
                epoch_train_loss += loss.item()
                train_batches += 1

        # Průměrné losses
        avg_train_loss = epoch_train_loss / train_batches if train_batches > 0 else 0
        avg_main_loss = epoch_main_loss / train_batches if train_batches > 0 else 0
        avg_aux_loss = epoch_aux_loss / train_batches if train_batches > 0 else 0

        train_losses.append(avg_train_loss)
        train_main_losses.append(avg_main_loss)
        train_aux_losses.append(avg_aux_loss)

        # Validace (stejná jako Option A)
        model.eval()
        epoch_val_loss = 0.0
        all_preds = []
        all_labels = []
        val_batches = 0

        for file in val_files:
            df = pd.read_csv(file)
            node_feats, edge_feats, adj, labels, val_mask, value_class_tensor = prepare_graph_data(
                df, device, max_key_class, max_value_class, max_result_class, is_training=True
            )

            with torch.no_grad():
                logits = model(node_feats, edge_feats, adj, value_class_tensor).squeeze(0)

                if val_mask.sum() > 0:
                    loss = criterion(logits[val_mask], labels[val_mask] - 1)
                    epoch_val_loss += loss.item()

                    preds = torch.argmax(logits[val_mask], dim=1)
                    all_preds.extend(preds.cpu().numpy())
                    all_labels.extend((labels[val_mask] - 1).cpu().numpy())
                    val_batches += 1

        # Zbytek stejný...
        avg_val_loss = epoch_val_loss / val_batches if val_batches > 0 else 0
        val_losses.append(avg_val_loss)

        if all_preds and all_labels:
            f1 = f1_score(all_labels, all_preds, average='weighted')
            val_f1_scores.append(f1)

            if f1 > best_f1:
                best_f1 = f1
                torch.save(model.state_dict(), os.path.join(output_dir, 'best_model.pth'))
                epochs_no_improve = 0
            else:
                epochs_no_improve += 1
        else:
            f1 = 0.0
            val_f1_scores.append(f1)

        # Rozšířený výpis pro Option B
        print(f'Epoch {epoch + 1}/{epochs}: '
              f'Total Loss: {avg_train_loss:.4f}, '
              f'Main: {avg_main_loss:.4f}, '
              f'Aux: {avg_aux_loss:.4f}, '
              f'Val Loss: {avg_val_loss:.4f}, '
              f'Val F1: {f1:.4f}')

        if epochs_no_improve >= patience:
            print(f'Early stopping after {epoch + 1} epochs')
            break

    # Rozšířená vizualizace pro Option B
    plt.figure(figsize=(15, 5))

    plt.subplot(1, 3, 1)
    plt.plot(train_losses, label='Total Train Loss')
    plt.plot(val_losses, label='Val Loss')
    plt.xlabel('Epoch')
    plt.ylabel('Loss')
    plt.title('Total Training and Validation Loss')
    plt.legend()
    plt.grid(True)

    plt.subplot(1, 3, 2)
    plt.plot(train_main_losses, label='Main Loss', color='blue')
    plt.plot(train_aux_losses, label='Auxiliary Loss', color='red')
    plt.xlabel('Epoch')
    plt.ylabel('Loss')
    plt.title('Main vs Auxiliary Loss Components')
    plt.legend()
    plt.grid(True)

    plt.subplot(1, 3, 3)
    plt.plot(val_f1_scores, label='Val F1', color='green')
    plt.xlabel('Epoch')
    plt.ylabel('F1 Score')
    plt.title('Validation F1 Score')
    plt.ylim([0, 1])
    plt.legend()
    plt.grid(True)

    plt.tight_layout()
    plt.savefig(os.path.join(output_dir, 'training_metrics_option_b.png'))
    plt.close()

    torch.save(model.state_dict(), os.path.join(output_dir, 'final_model.pth'))
    print(f'Training complete. Best validation F1: {best_f1:.4f}')

    return train_losses, val_losses, val_f1_scores, train_main_losses, train_aux_losses


# =============================================================================
# POUŽITÍ
# =============================================================================

# Option A - jednodušší
# train_losses, val_losses, val_f1_scores = train_model_option_a(
#     model, train_files, val_files, device, max_key_class, max_value_class, max_result_class,
#     penalty_factor=5.0, use_affinity=True
# )

# Option B - sofistikovanější
# train_losses, val_losses, val_f1_scores, main_losses, aux_losses = train_model_option_b(
#     model, train_files, val_files, device, max_key_class, max_value_class, max_result_class,
#     lambda_affinity=0.5, use_affinity=True
# )


# Konfigurace
if __name__ == "__main__":
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')

    # Maximální hodnoty tříd z datasetu
    MAX_KEY_CLASS = len(utils.KEY_CLASS_REGISTRY)
    MAX_VALUE_CLASS = len(utils.VALUE_CLASS_REGISTRY)
    MAX_RESULT_CLASS = len(utils.RESULT_CLASS_REGISTRY) - 1

    # Inicializace modelu
    node_in_dim = calculate_node_dim(MAX_KEY_CLASS, MAX_VALUE_CLASS)
    model = GatNodeClassifier(
        node_in_dim=node_in_dim,
        output_dim=MAX_RESULT_CLASS  # 18 výstupních tříd
    ).to(device)

    # Trénink
    train_files = ['Pohoda03.csv', 'Pohoda04.csv']  # Seznam tréninkových souborů
    train_model(model, train_files, device, MAX_KEY_CLASS, MAX_VALUE_CLASS, MAX_RESULT_CLASS)

    # Uložení modelu
    torch.save(model.state_dict(), 'gat_classifier.pth')

    # Predikce na novém dokumentu
    new_df = pd.read_csv('NewDocument.csv')
    predictions = predict(model, new_df, device, MAX_KEY_CLASS, MAX_VALUE_CLASS, MAX_RESULT_CLASS)
    new_df['predicted_class'] = predictions
    new_df.to_csv('NewDocument_with_predictions.csv', index=False)