#!/usr/bin/env python3
"""
Příklad použití typové matice v GAT klasifikátoru.
"""

import sys
import pandas as pd
import numpy as np
import torch

# Přidáme cesty pro importy
sys.path.append('.')
sys.path.append('GAT_DS')

from GAT_DS.GATClassifier import (
    create_type_affinity_matrix, 
    prepare_graph_data, 
    GatNodeClassifier, 
    calculate_node_dim,
    predict
)
from utils.utils import KEY_CLASS_REGISTRY, VALUE_CLASS_REGISTRY, RESULT_CLASS_REGISTRY

def demonstrate_type_affinity():
    """Demonstrace funkčnosti typové matice."""
    print("🎯 Demonstrace typové matice pro GAT klasifikátor")
    print("=" * 60)
    
    # 1. Vytvoření typové matice
    print("\n1️⃣ Vytvoření typové matice")
    print("-" * 30)
    
    max_key_class = 26
    max_value_class = 8
    
    affinity_matrix = create_type_affinity_matrix(max_key_class, max_value_class)
    print(f"✓ Typová matice vytvořena: {affinity_matrix.shape}")
    
    # 2. Zobrazení některých omezení
    print("\n2️⃣ Příklady typových omezení")
    print("-" * 30)
    
    examples = [
        (6, 5, "Číslo faktury", "Alfanumerický kód"),
        (6, 1, "Číslo faktury", "Datum"),
        (13, 1, "Datum vystavení", "Datum"),
        (13, 8, "Datum vystavení", "Číslo"),
        (19, 8, "IČO", "Číslo"),
        (21, 7, "DIČ", "DIČ"),
        (22, 6, "IBAN", "IBAN"),
    ]
    
    for key_id, value_id, key_name, value_name in examples:
        allowed = affinity_matrix[key_id, value_id] == 1.0
        status = "✅ POVOLENO" if allowed else "❌ ZAKÁZÁNO"
        print(f"  {key_name} -> {value_name}: {status}")
    
    # 3. Vytvoření testovacích dat
    print("\n3️⃣ Testovací data")
    print("-" * 30)
    
    test_data = {
        'text': [
            'page',           # řádek stránky
            'Faktura č.:',    # klíč pro číslo faktury
            'F-2024-001',     # alfanumerická hodnota faktury
            'Datum vystavení:', # klíč pro datum
            '15.01.2024',     # datum
            'IČO:',           # klíč pro IČO
            '12345678',       # číselné IČO
            'DIČ:',           # klíč pro DIČ
            'CZ12345678',     # DIČ hodnota
        ],
        'left': [0, 50, 150, 50, 150, 50, 150, 50, 150],
        'top': [0, 100, 100, 130, 130, 160, 160, 190, 190],
        'width': [800, 80, 100, 100, 80, 30, 80, 30, 90],
        'height': [1200, 20, 20, 20, 20, 20, 20, 20, 20],
        'key_class': [1, 6, 0, 13, 0, 19, 0, 21, 0],  # strana, číslo faktury, neznámý, datum vystavení, neznámý, IČO, neznámý, DIČ, neznámý
        'value_class': [0, 0, 5, 0, 1, 0, 8, 0, 7],   # neznámý, neznámý, alfanumerický, neznámý, datum, neznámý, číslo, neznámý, DIČ
        'result_class': [0, 0, 1, 0, 2, 0, 8, 0, 7]   # pro testování
    }
    
    df = pd.DataFrame(test_data)
    print(f"✓ Testovací DataFrame: {len(df)} řádků")
    
    # Zobrazení dat
    print("\nTestovací data:")
    for i, row in df.iterrows():
        if row['key_class'] > 0:
            key_name = KEY_CLASS_REGISTRY.get(row['key_class'], 'neznámý')
            print(f"  Klíč: {row['text']} ({key_name})")
        elif row['value_class'] > 0:
            value_name = VALUE_CLASS_REGISTRY.get(row['value_class'], 'neznámý')
            print(f"    -> Hodnota: {row['text']} ({value_name})")
    
    # 4. Příprava dat pro GAT
    print("\n4️⃣ Příprava dat pro GAT s typovou maticí")
    print("-" * 30)
    
    device = torch.device('cpu')
    max_result_class = 18
    
    try:
        node_features, edge_features, adj, labels, train_mask, value_class_tensor = prepare_graph_data(
            df, device, max_key_class, max_value_class, max_result_class, is_training=False
        )
        
        print(f"✓ Data připravena pro GAT")
        print(f"  - Node features: {node_features.shape}")
        print(f"  - Edge features: {edge_features.shape} (včetně typové afinity)")
        
        # Extrakce typové afinity
        edge_feats_np = edge_features.squeeze(0).cpu().numpy()
        type_affinity_feature = edge_feats_np[:, :, 6]  # 7. feature (index 6)
        
        print(f"\n📊 Typové afinity v datech:")
        for i in range(len(df)):
            for j in range(len(df)):
                if df.iloc[i]['key_class'] > 0 and df.iloc[j]['value_class'] > 0:
                    key_name = KEY_CLASS_REGISTRY.get(df.iloc[i]['key_class'], 'neznámý')
                    value_name = VALUE_CLASS_REGISTRY.get(df.iloc[j]['value_class'], 'neznámý')
                    affinity = type_affinity_feature[i, j]
                    status = "✅" if affinity > 0 else "❌"
                    print(f"  {status} {key_name} -> {value_name}: {affinity}")
        
    except Exception as e:
        print(f"❌ Chyba při přípravě dat: {e}")
        return
    
    # 5. Vytvoření a test modelu
    print("\n5️⃣ Test s GAT modelem")
    print("-" * 30)
    
    try:
        # Výpočet dimenze vstupních features
        node_in_dim = calculate_node_dim(max_key_class, max_value_class)
        
        # Vytvoření modelu
        model = GatNodeClassifier(
            node_in_dim=node_in_dim,
            hidden_dim=32,
            heads=2,
            output_dim=max_result_class,
            num_value_classes=max_value_class + 1,
            num_layers=1,
            dropout_rate=0.1
        )
        
        print(f"✓ GAT model vytvořen")
        print(f"  - Node input dim: {node_in_dim}")
        print(f"  - Edge input dim: {model.layers[0].linear_edges.in_features}")
        
        # Test forward pass
        model.eval()
        with torch.no_grad():
            outputs = model(node_features, edge_features, adj, value_class_tensor)
            print(f"✓ Forward pass úspěšný: {outputs.shape}")
        
        # Simulace predikce
        predictions, confidences = predict(
            model, df, device, max_key_class, max_value_class, max_result_class
        )
        
        print(f"✓ Predikce dokončena")
        print(f"  - Predictions shape: {predictions.shape}")
        print(f"  - Confidences shape: {confidences.shape}")
        
    except Exception as e:
        print(f"❌ Chyba při práci s modelem: {e}")
        return
    
    print("\n🎉 Demonstrace úspěšně dokončena!")
    print("Typová matice je plně funkční a integrovaná do GAT klasifikátoru.")

def show_affinity_matrix_summary():
    """Zobrazí přehled typové matice."""
    print("\n📋 Přehled typové matice")
    print("=" * 40)
    
    max_key_class = 26
    max_value_class = 8
    
    matrix = create_type_affinity_matrix(max_key_class, max_value_class)
    
    print(f"Rozměry matice: {matrix.shape}")
    print(f"Celkem vztahů: {matrix.size}")
    print(f"Povolených vztahů: {int(matrix.sum())}")
    print(f"Zakázaných vztahů: {int(matrix.size - matrix.sum())}")
    
    # Statistiky pro jednotlivé klíče
    print(f"\n📊 Statistiky pro klíče:")
    for key_id in [6, 7, 13, 19, 21, 22]:  # vybrané klíče
        if key_id in KEY_CLASS_REGISTRY:
            key_name = KEY_CLASS_REGISTRY[key_id]
            allowed_count = int(matrix[key_id, :].sum())
            print(f"  {key_name} ({key_id}): {allowed_count} povolených typů hodnot")

if __name__ == "__main__":
    demonstrate_type_affinity()
    show_affinity_matrix_summary()
