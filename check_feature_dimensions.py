#!/usr/bin/env python3
"""
Rychlý test pro zjištění přesných dimenzí features.
"""

import sys
import pandas as pd

sys.path.append('.')
sys.path.append('GAT')

from GAT.train_gat import prepare_spatial_features, prepare_class_features
from improved_noise_filters import add_noise_robustness_features

# Načteme testovací data
df = pd.read_csv("Tests/Faktura.csv")
print(f"Původní data: {len(df)} řádků, {len(df.columns)} sloupců")
print(f"Sloupce: {list(df.columns)}")

# 1. Prostorové features
df = prepare_spatial_features(df)
print(f"\nPo spatial features: {len(df.columns)} sloupců")

# 2. Class features
df, feature_names = prepare_class_features(df)
print(f"Po class features: {len(df.columns)} sloupců")
print(f"Feature names: {feature_names}")

# 3. Noise robustness features
df = add_noise_robustness_features(df)
print(f"Po noise features: {len(df.columns)} sloupců")

# Spočítáme features pro GAT
feature_columns = [col for col in df.columns if col not in ['text', 'left', 'top', 'width', 'height', 'source_file']]
print(f"\nFeatures pro GAT: {len(feature_columns)} sloupců")
print(f"GAT feature columns: {feature_columns}")
