import sys
import pandas as pd
import numpy as np
import cv2
from PyQt5 import QtWidgets, QtCore
import pyqtgraph as pg

# Barvy podle labelu
LABEL_COLORS = {
    "Key": 'lime',
    "Value": 'blue',
    "Result": 'yellow',
    "Ignore": 'gray',
    None: 'g'
}

class OcrPreviewQt(QtWidgets.QWidget):
    def __init__(self, df: pd.DataFrame, image: np.ndarray, hide_threshold=500):
        super().__init__()
        self.df = df.copy()
        if "label" not in self.df.columns:
            self.df["label"] = None

        self.image = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)
        self.hide_threshold = hide_threshold  # výška ViewBox pro skrytí textů
        self.init_ui()

    def init_ui(self):
        layout = QtWidgets.QVBoxLayout()
        self.setLayout(layout)

        self.view = pg.GraphicsLayoutWidget()
        layout.addWidget(self.view)
        self.plot = self.view.addViewBox()
        self.plot.setAspectLocked(True)
        self.plot.setMouseEnabled(x=True, y=True)
        self.plot.setAcceptedMouseButtons(QtCore.Qt.LeftButton)

        # Obrázek
        self.img_item = pg.ImageItem(self.image)
        self.plot.addItem(self.img_item)

        # Rects a texty
        self.rect_items = []
        self.text_items = []
        self.label_text_items = []
        for idx, row in self.df.iterrows():
            # obdélník
            rect = pg.RectROI([row['left'], row['top']],
                              [row['right']-row['left'], row['bottom']-row['top']],
                              pen=pg.mkPen(LABEL_COLORS[None], width=2), movable=False)
            self.plot.addItem(rect)
            self.rect_items.append((rect, idx))

            # text Node nad obdélníkem, menší font
            text_item = pg.TextItem(row['text'], color='w', anchor=(0,1),
                                    border='k', fill=pg.mkBrush(0,0,0,150))

            font = QtWidgets.QApplication.font()
            font.setPointSize(int(font.pointSize() * 0.6))  # zmenšení
            text_item.setFont(font) # zmenšení fontu
            text_item.setPos(row['left'], row['top'] - 2)
            self.plot.addItem(text_item)
            self.text_items.append(text_item)

            # text label pod obdélníkem, menší font
            label_text_item = pg.TextItem("", color='k', anchor=(0,0),
                                          border='w', fill=pg.mkBrush(255,255,255,150))
            font = QtWidgets.QApplication.font()
            font.setPointSize(int(font.pointSize() * 0.6))  # zmenšení
            text_item.setFont(font)
            label_text_item.setPos(row['left'], row['bottom'] + 2)
            self.plot.addItem(label_text_item)
            self.label_text_items.append(label_text_item)

        # kliknutí
        self.proxy = pg.SignalProxy(self.plot.scene().sigMouseClicked, rateLimit=60, slot=self.on_click)

        # sledování zoomu pro skrývání textů
        self.plot.sigXRangeChanged.connect(self.on_view_changed)
        self.plot.sigYRangeChanged.connect(self.on_view_changed)

        self.setWindowTitle("OCR Preview")
        self.resize(800, 600)
        self.show()

    def on_view_changed(self):
        view_rect = self.plot.viewRect()
        scale_factor = view_rect.height()
        show_text = scale_factor < self.hide_threshold
        for t in self.text_items + self.label_text_items:
            t.setVisible(show_text)

    def on_click(self, event):
        mouse_event = event[0]
        if mouse_event.button() != QtCore.Qt.LeftButton:
            return
        pos = mouse_event.scenePos()
        mouse_point = self.plot.mapSceneToView(pos)
        x, y = mouse_point.x(), mouse_point.y()

        for rect, idx in self.rect_items:
            x0, y0 = rect.pos()
            w, h = rect.size()
            if x0 <= x <= x0 + w and y0 <= y <= y0 + h:
                self.show_dropdown(idx)
                break

    def show_dropdown(self, idx):
        options = ["Key", "Value", "Result", "Ignore"]
        item_text = self.df.at[idx, "text"]

        dialog = QtWidgets.QDialog(self)
        dialog.setWindowTitle(f"Anotace: {item_text}")
        layout = QtWidgets.QVBoxLayout()
        dialog.setLayout(layout)

        combo = QtWidgets.QComboBox()
        combo.addItems(options)
        layout.addWidget(combo)
        combo.setCurrentText("Ignore" if self.df.at[idx, "label"] is None else self.df.at[idx, "label"])

        btn = QtWidgets.QPushButton("OK")
        layout.addWidget(btn)

        def confirm():
            choice = combo.currentText()
            self.df.at[idx, "label"] = choice
            rect, _ = self.rect_items[idx]
            rect.setPen(pg.mkPen(LABEL_COLORS[choice], width=2))
            # aktualizace textu labelu
            self.label_text_items[idx].setText(choice)
            print(f"Node '{item_text}' anotován jako: {choice}")
            dialog.accept()

        btn.clicked.connect(confirm)
        dialog.exec_()


# --- příklad použití ---
if __name__ == "__main__":
    data = [
        {"text": "Celkem", "left": 50, "top": 100, "right": 120, "bottom": 120},
        {"text": "1000",   "left": 150, "top": 100, "right": 200, "bottom": 120},
    ]
    df = pd.DataFrame(data)

    img = 255 * np.ones((300, 400, 3), dtype=np.uint8)

    app = QtWidgets.QApplication(sys.argv)
    preview = OcrPreviewQt(df, img)
    sys.exit(app.exec_())
