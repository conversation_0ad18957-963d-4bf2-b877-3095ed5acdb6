# OCRProcessor Refactoring - Rozd<PERSON><PERSON><PERSON> zodpovědností

## Přehled změn

<PERSON> `OCRProcessor` byla refaktorována pro jasné rozdělení zodpovědností a vytvoření strukturované pipeline pro zpracování OCR dat.

## Nová architektura

### 1. Pipeline struktura

```
Vstup (PDF/Image) 
    ↓
Základní OCR (get_raw_ocr_data)
    ↓
Čištění - Sanitization (sanitize_data)
    ↓
Spojování - Merging (merge_tokens)
    ↓
Rozdělování - Splitting (split_tokens)
    ↓
Page marker (add_page_marker)
    ↓
Výstup (DataFrame)
```

### 2. <PERSON><PERSON> metody

#### `get_raw_ocr_data(image=None) -> (DataFrame, np.ndarray)`
- **Zodpovědnost**: Pouze základní OCR bez dalšího zpracování
- **Výstup**: Surová OCR data + zpracovaný obrázek
- **Použití**: Základ pipeline

#### `sanitize_data(df) -> DataFrame`
- **Zodpovědnost**: Základní čištění dat
- **Operace**: 
  - Odstranění NaN hodnot
  - Oříznutí bílých znaků
  - Unicode normalizace
  - Filtrování nízké konfidence

#### `merge_tokens(df) -> DataFrame`
- **Zodpovědnost**: Slučování sousedních tokenů
- **Deleguje na**: `utils.merge_texts()`
- **Výstup**: Super-tokeny

#### `split_tokens(df, image) -> DataFrame`
- **Zodpovědnost**: Rozdělování tokenů s delimitery
- **Deleguje na**: `utils.split_by_delimiter()`
- **Klíčová změna**: Předává `self` pro opakované použití instance

#### `add_page_marker(df, image) -> DataFrame`
- **Zodpovědnost**: Přidání speciálního řádku 'page'
- **Výstup**: DataFrame s page markerem

#### `process_document_pipeline(image=None) -> DataFrame`
- **Zodpovědnost**: Kompletní pipeline v jednom volání
- **Použití**: Hlavní vstupní bod pro zpracování

#### `get_character_positions(bbox, image) -> list`
- **Zodpovědnost**: Detekce pozic jednotlivých znaků
- **Klíčová změna**: Používá konfiguraci instance místo hardcoded hodnot

### 3. Zachování kompatibility

#### `get_items(image=None) -> DataFrame`
- **Změna**: Nyní volá `process_document_pipeline()`
- **Kompatibilita**: Zachována - existující kód funguje beze změn

## Výhody nové architektury

### 1. Jasně oddělené zodpovědnosti
- Každá metoda má jednu jasně definovanou úlohu
- Snadnější testování a debugging
- Lepší udržovatelnost kódu

### 2. Opakované použití instance
- Jedna instance OCRProcessor pro celý proces
- Konzistentní konfigurace (language, dpi, oem, psm)
- Eliminace opakované inicializace Tesseractu

### 3. Flexibilita
- Možnost spuštění celé pipeline nebo jednotlivých kroků
- Snadné přidání nových kroků do pipeline
- Konfigurovatelné chování

### 4. Lepší performance
- Méně vytváření objektů
- Opakované použití stejné konfigurace
- Efektivnější využití paměti

## Změny v utils.py

### `split_by_delimiter(df, image, delimiters=None, ocr_processor=None)`
- **Nový parametr**: `ocr_processor` - volitelná instance OCRProcessor
- **Chování**: Pokud je poskytnut, používá jeho `get_character_positions()`
- **Fallback**: Vytvoří dočasnou instanci pokud není poskytnut

### `get_character_level_text(bbox, img, ocr_processor=None)`
- **Nový parametr**: `ocr_processor` - volitelná instance OCRProcessor
- **Chování**: Používá konfiguraci poskytnuté instance
- **Fallback**: Původní hardcoded konfigurace

## Změny v OCR/ocr.py

### Nové funkce

#### `do_with_ocr_instance(file_path, ocr_processor)`
- Zpracování s existující instancí OCRProcessor
- Užitečné pro batch zpracování

#### `get_ocr_instance(language, dpi, oem, psm)`
- Factory metoda pro vytvoření OCRProcessor instance
- Centralizované vytváření s konfigurací

## Změny v process_document.py

### Zjednodušení pipeline
- OCR pipeline nyní probíhá v jednom kroku
- Eliminace redundantního volání `merge_texts()` a `split_by_delimiter()`
- Jasnější logika zpracování

## Příklady použití

### Základní použití (zachována kompatibilita)

```python
from OCR import ocr_old

df = ocr.do('document.pdf')  # Funguje stejně jako dříve
```

### Pokročilé použití s jednou instancí
```python
from OCR.OCRProcessor import OCRProcessor

# Vytvoření instance
ocr = OCRProcessor(language='ces', dpi=300)

# Zpracování více dokumentů
for doc_path in documents:
    ocr.load_pdf(doc_path)
    df = ocr.process_document_pipeline()
    # Zpracování výsledků...
```

### Krokové zpracování
```python
ocr = OCRProcessor()
ocr.load_pdf('document.pdf')

# Jednotlivé kroky
raw_df, image = ocr.get_raw_ocr_data()
clean_df = ocr.sanitize_data(raw_df)
merged_df = ocr.merge_tokens(clean_df)
split_df = ocr.split_tokens(merged_df, image)
final_df = ocr.add_page_marker(split_df, image)
```

## Testování

Pro testování nové architektury spusťte:
```bash
python OCR/pipeline_example.py
```

Tento skript demonstruje všechny nové funkce a porovnává různé konfigurace.

## Migrace existujícího kódu

### Žádné změny potřebné
Existující kód používající `ocr.do()` funguje beze změn.

### Doporučené vylepšení
Pro lepší performance při zpracování více dokumentů:

```python
# Místo:
for doc in documents:
    df = ocr.do(doc)

# Použijte:
ocr_instance = ocr.get_ocr_instance()
for doc in documents:
    df = ocr.do_with_ocr_instance(doc, ocr_instance)
```

## Budoucí rozšíření

Nová architektura umožňuje snadné přidání:
- Dalších kroků do pipeline
- Různých strategií pro merging/splitting
- Pokročilých filtrů v sanitization
- Metriky a monitoring jednotlivých kroků
