import re
import unicodedata
from typing import Tuple, List, Optional

import numpy as np
import pandas as pd
import pytesseract


def contains_digits(text: str) -> bool:
    return bool(re.search(r'\d', text))


def contains_letters(text: str) -> bool:
    return any(unicodedata.category(char).startswith('L') for char in text)


def is_number(text: str) -> bool:
    has_digits = contains_digits(text)
    has_punctuation = bool(re.search(r'[-.,]', text))
    return has_digits and has_punctuation


def is_alphanumeric_code(text: str) -> bool:
    """Detekuje alfanumerický kód - text obsahující jak písmena, tak číslice"""
    has_letters = contains_letters(text)
    has_digits = contains_digits(text)
    return has_letters and has_digits


def is_alfanumeric_code_with_punctuation(text: str) -> bool:
    """Detekuje alfanumerický kód s interpunkčními znaky - písmena, číslice a znaky jako <PERSON>, pomlčka apod."""
    has_letters = contains_letters(text)
    has_digits = contains_digits(text)
    has_punctuation = bool(re.search(r'[/\-_.:\\\|~]', text))
    return has_letters and has_digits and has_punctuation


def is_numeric_code_with_punctuation(text: str) -> bool:
    """Detekuje numerický kód s interpunkčními znaky - číslice a znaky jako lomítko, pomlčka apod."""
    has_digits = contains_digits(text)
    has_punctuation = bool(re.search(r'[/\-_:\\|~]', text))
    return has_digits and has_punctuation


def merge_texts(df: pd.DataFrame) -> pd.DataFrame:
    """
    Sloučí sousední textové prvky, ale pouze pro řádky s level == 5 (word-level).
    Ostatní řádky (např. rozdělené tokeny s level == 6 nebo speciální "page") zůstanou zachovány.
    """
    if df is None or df.empty:
        return df

    words_df = df.sort_values(by=['top', 'left']).reset_index(drop=True)

    merged_rows: List[List] = []
    buffer_texts: List[str] = []
    buffer_left = buffer_top = buffer_right = buffer_bottom = None

    def add_to_buffer(text, left, top, width, height):
        nonlocal buffer_texts, buffer_left, buffer_top, buffer_right, buffer_bottom
        if not buffer_texts:
            buffer_texts = [text]
            buffer_left = left
            buffer_top = top
            buffer_right = left + width
            buffer_bottom = top + height
        else:
            buffer_texts.append(text)
            buffer_right = max(buffer_right, left + width)
            buffer_bottom = max(buffer_bottom, top + height)

    def flush_buffer():
        nonlocal buffer_texts, buffer_left, buffer_top, buffer_right, buffer_bottom
        if buffer_texts:
            merged_text = ' '.join(buffer_texts)
            merged_width = buffer_right - buffer_left
            merged_height = buffer_bottom - buffer_top
            merged_rows.append([merged_text, buffer_left, buffer_top, merged_width, merged_height])
        buffer_texts = []
        buffer_left = buffer_top = buffer_right = buffer_bottom = None

    for i in range(len(words_df)):
        current_text = words_df.loc[i, 'text']
        current_left = words_df.loc[i, 'left']
        current_top = words_df.loc[i, 'top']
        current_width = words_df.loc[i, 'width']
        current_height = words_df.loc[i, 'height']

        if i < len(words_df) - 1:
            next_left = words_df.loc[i + 1, 'left']
            next_top = words_df.loc[i + 1, 'top']
            next_width = words_df.loc[i + 1, 'width']
            next_height = words_df.loc[i + 1, 'height']
            next_text = words_df.loc[i + 1, 'text']

            line_threshold = max(current_height, next_height) * 2
            same_line = abs(current_top - next_top) < line_threshold
            distance = next_left - (current_left + current_width) if same_line else float('inf')

            current_is_digit = current_text.isdigit()
            current_is_number = is_number(current_text)
            next_is_digit = next_text.isdigit()
            current_has_letters = contains_letters(current_text)
            current_is_currency = current_text in ["CZK", "EUR", "USD", "GBP", "JPY", "HUF", "CHF", "PLN"]
            next_has_letters = contains_letters(next_text)
            next_is_alphanumeric = is_alphanumeric_code(next_text)
            next_is_alfanumeric_with_punctuation = is_alfanumeric_code_with_punctuation(next_text)
            next_is_numeric_with_punctuation = is_numeric_code_with_punctuation(next_text)
            next_is_currency = next_text in ["CZK", "EUR", "USD", "GBP", "JPY", "HUF", "CHF", "PLN"]

            should_merge = (
                0 < distance < current_height * 1.5 and
                not current_text.endswith((':', ';', ')')) and
                not next_text.startswith((':', ';', '(')) and
                not (current_is_digit and next_has_letters) and
                not (current_is_number and next_has_letters) and
                not (current_has_letters and next_is_digit) and
                not (current_has_letters and next_is_alphanumeric) and
                not (current_has_letters and next_is_alfanumeric_with_punctuation) and
                not (current_has_letters and next_is_numeric_with_punctuation) and
                not (current_has_letters and next_is_currency) and
                not (current_is_currency and next_has_letters)
            )

            if should_merge:
                add_to_buffer(current_text, current_left, current_top, current_width, current_height)
                continue

        add_to_buffer(current_text, current_left, current_top, current_width, current_height)
        flush_buffer()

    flush_buffer()

    merged_df = pd.DataFrame(merged_rows, columns=['text', 'left', 'top', 'width', 'height'])

    return merged_df


def _char_boxes_from_roi(roi: np.ndarray) -> List[dict]:
    """Získá character-level boxy z ROI pomocí pytesseract.image_to_boxes a převede je na seznam dictů.
    Souřadnice jsou vztažené k ROI (0,0 nahoře vlevo).
    """
    char_config = r''' --oem 1 --psm 8 --dpi 300 -l ces+eng -c preserve_interword_spaces=1 '''.replace('\n', ' ')
    box_data = pytesseract.image_to_boxes(roi, config=char_config)
    boxes = []
    roi_height = roi.shape[0]
    for line in box_data.strip().split('\n'):
        if line.strip():
            parts = line.split()
            if len(parts) >= 6:
                ch = parts[0]
                left = int(parts[1])
                bottom = int(parts[2])
                right = int(parts[3])
                top = int(parts[4])
                converted_top = roi_height - top
                converted_bottom = roi_height - bottom
                boxes.append({
                    'char': ch,
                    'left': left,
                    'top': converted_top,
                    'right': right,
                    'bottom': converted_bottom,
                })
    return boxes


def split_by_delimiter(df: pd.DataFrame, image: np.ndarray, delimiters: Optional[list] = None, ocr_processor=None) -> pd.DataFrame:
    """
    Rozdělí texty obsahující oddělovač na dva samostatné tokeny.
    Pokud je poskytnut ocr_processor, použije jeho konfiguraci; jinak fallback přes pytesseract přímo nad ROI.
    """
    if df is None or df.empty:
        return df

    if delimiters is None:
        delimiters = ['|', ':', '(']

    all_rows_to_split = []
    for delimiter in delimiters:
        delimiter_mask = df['text'].astype(str).str.contains(re.escape(delimiter), na=False)
        potential_rows = df[delimiter_mask].copy()
        for idx, row in potential_rows.iterrows():
            text = str(row['text'])
            if delimiter in text and not text.startswith(delimiter) and not text.endswith(delimiter):
                parts = text.split(delimiter, 1)
                if len(parts) == 2 and parts[0].strip() and parts[1].strip():
                    all_rows_to_split.append((idx, row, delimiter))

    if not all_rows_to_split:
        return df

    new_rows = []
    rows_to_remove = []

    for idx, row, delimiter in all_rows_to_split:
        text = str(row['text'])
        parts = text.split(delimiter, 1)
        if len(parts) != 2:
            continue
        before_delimiter = parts[0].strip()
        after_delimiter = parts[1].strip()
        if not before_delimiter or not after_delimiter:
            continue
        try:
            x, y, w, h = int(row['left']), int(row['top']), int(row['width']), int(row['height'])
            roi = image[y:y + h, x:x + w]

            if ocr_processor is not None:
                char_positions_abs = ocr_processor.get_character_positions((x, y, w, h), image)
                if not char_positions_abs:
                    continue
                char_positions = []
                for ch in char_positions_abs:
                    char_positions.append({
                        'char': ch['char'],
                        'left': ch['left'] - x,
                        'right': ch['right'] - x,
                        'top': ch['top'] - y,
                        'bottom': ch['bottom'] - y,
                    })
            else:
                char_positions = _char_boxes_from_roi(roi)
                if not char_positions:
                    continue

            detected_text = ''.join([c['char'] for c in char_positions])
            delimiter_pos = detected_text.find(delimiter)
            if delimiter_pos == -1:
                continue
            delimiter_start_idx = delimiter_pos
            delimiter_end_idx = delimiter_pos + len(delimiter) - 1

            chars_before = char_positions[:delimiter_start_idx]
            chars_after = char_positions[delimiter_end_idx + 1:]
            if not chars_before or not chars_after:
                continue

            new_row_before = row.copy()
            new_row_before['text'] = ''.join([c['char'] for c in chars_before]).strip()
            new_row_before['left'] = x + chars_before[0]['left']
            new_row_before['width'] = chars_before[-1]['right'] - chars_before[0]['left']
            new_row_before['level'] = 6

            new_row_after = row.copy()
            new_row_after['text'] = ''.join([c['char'] for c in chars_after]).strip()
            new_row_after['left'] = x + chars_after[0]['left']
            new_row_after['width'] = chars_after[-1]['right'] - chars_after[0]['left']
            new_row_after['level'] = 6

            if new_row_before['text']:
                new_rows.append(new_row_before)
            if new_row_after['text']:
                new_rows.append(new_row_after)
            rows_to_remove.append(idx)
        except Exception:
            continue

    if rows_to_remove:
        df = df.drop(rows_to_remove)
    if new_rows:
        new_df = pd.DataFrame(new_rows)
        df = pd.concat([df, new_df], ignore_index=True)

    return df


def clean_texts(df: pd.DataFrame, text_column: str = 'text') -> pd.DataFrame:
    """
    Čistí texty v DataFrame - nahrazuje měnové symboly a odstraňuje přebytečné mezery.
    NEODSTRAŇUJE nevalidní texty - to se provádí až po klasifikaci pomocí remove_invalid_texts().
    """
    def clean_text(text: str) -> str:
        text = re.sub(r'Kč', 'CZK', text)
        text = re.sub(r'Ké', 'CZK', text)
        text = re.sub(r'Kě', 'CZK', text)
        text = re.sub(r'Kc', 'CZK', text)
        text = re.sub(r'€', 'EUR', text)
        text = re.sub(r'\$', 'USD', text)
        text = re.sub(r'£', 'GBP', text)
        text = re.sub(r'¥', 'JPY', text)
        text = re.sub(r'Ft', 'HUF', text)
        text = re.sub(r'Fr\.', 'CHF', text)
        text = re.sub(r'SFr', 'CHF', text)
        text = re.sub(r'zł', 'PLN', text)
        text = re.sub(r'dph', 'DPH', text)
        text = re.sub(r'Dph', 'DPH', text)
        text = re.sub(r'vat', 'VAT', text)
        text = re.sub(r'[,:;—-‘"+/&<>~¢|®©]', ' ', text)
        text = re.sub(r'\*', ' ', text).strip()
        text = re.sub(r'\(', ' ', text).strip()
        text = re.sub(r'\)', ' ', text).strip()
        text = re.sub(r'\[', ' ', text).strip()
        text = re.sub(r']', ' ', text).strip()
        text = re.sub(r'\\', ' ', text).strip()
        text = re.sub(r'!', ' ', text).strip()
        text = re.sub(r'\?', ' ', text).strip()
        text = re.sub(r'\s+', ' ', text).strip()
        return text.strip()

    cleaned_series = df[text_column].copy()
    mask = df['value_class'] == 0
    cleaned_series[mask] = df.loc[mask, text_column].apply(clean_text)
    df[text_column] = cleaned_series
    print("Text cleaning completed (currency symbols replaced)")
    return df

def normalize_texts(df: pd.DataFrame, text_column: str = 'text') -> pd.DataFrame:
    """
    Čistí texty v DataFrame - nahrazuje měnové symboly a odstraňuje přebytečné mezery.
    NEODSTRAŇUJE nevalidní texty - to se provádí až po klasifikaci pomocí remove_invalid_texts().
    """
    def clean_text(text: str) -> str:
        text = re.sub(r'Kč', 'CZK', text)
        text = re.sub(r'Ké', 'CZK', text)
        text = re.sub(r'Kě', 'CZK', text)
        text = re.sub(r'Kc', 'CZK', text)
        text = re.sub(r'€', 'EUR', text)
        text = re.sub(r'\$', 'USD', text)
        text = re.sub(r'£', 'GBP', text)
        text = re.sub(r'¥', 'JPY', text)
        text = re.sub(r'Ft', 'HUF', text)
        text = re.sub(r'Fr\.', 'CHF', text)
        text = re.sub(r'SFr', 'CHF', text)
        text = re.sub(r'zł', 'PLN', text)
        text = re.sub(r'dph', 'DPH', text)
        text = re.sub(r'Dph', 'DPH', text)
        text = re.sub(r'vat', 'VAT', text)
        text = re.sub(r'Vat', 'VAT', text)
        text = re.sub(r'D\|Č', 'DIČ', text)
        text = re.sub(r'DIG', 'DIČ', text)
        text = re.sub(r'DIC', 'DIČ', text)
        text = re.sub(r'Ič', 'IČO', text)
        text = re.sub(r'IC', 'IČO', text)
        text = re.sub(r'lČ', 'IČO', text)
        text = re.sub(r'[,:;—-‘"+/&<>~¢|®©]', ' ', text)
        text = re.sub(r'\*', ' ', text).strip()
        text = re.sub(r'\(', ' ', text).strip()
        text = re.sub(r'\)', ' ', text).strip()
        text = re.sub(r'\[', ' ', text).strip()
        text = re.sub(r']', ' ', text).strip()
        text = re.sub(r'\\', ' ', text).strip()
        text = re.sub(r'!', ' ', text).strip()
        text = re.sub(r'\?', ' ', text).strip()
        text = re.sub(r'\s+', ' ', text).strip()
        return text.strip()

    df[text_column] = df[text_column].apply(clean_text)

    return df
