import re
import unicodedata

import cv2
import numpy as np
import pandas as pd

from OCR.OcrEngine import OcrEngine


class OcrPipeline:
    def __init__(self, lang: str = "ces"):
        self.engine = OcrEngine(lang)

    def load_image(self, image_path: str) -> None:
        """
        Načte obrázek pro zpracování.
        """
        image = cv2.imread(image_path)
        self.process(image)

    def process(self, image: cv2.Mat) -> pd.DataFrame:
        """
        Kompletní pipeline: OCR + čištění + spojování.
        """
        df = self.engine.process_data(image)
        df = self._clean_text(df)

        # převést DataFrame do listu slov
        words = [
            {
                "text": row["text"],
                "bbox": (
                    row["left"],
                    row["top"],
                    row["left"] + row["width"],
                    row["top"] + row["height"],
                )
            }
            for _, row in df.iterrows()
        ]

        merged_words = self.merge_words(words)
        return pd.DataFrame(merged_words)

    @staticmethod
    def _clean_text(df: pd.DataFrame) -> pd.DataFrame:
        """
        Čistí a normalizuje texty v DataFrame.
        """
        df_clean = df.copy()

        # unicode normalizace
        df_clean["text"] = df_clean["text"].apply(
            lambda x: unicodedata.normalize("NFC", x)
        )

        return df_clean

    # --- spojování slov ---
    @staticmethod
    def _should_merge_words(cur_text: str, nxt_text: str) -> bool:
        cur_text = cur_text.strip()
        nxt_text = nxt_text.strip()

        # 1. končí : nebo ;
        if cur_text.endswith((':', ';')):
            return False

        # 2. aktuální je číslo a následující obsahuje písmena
        if re.fullmatch(r"\d+", cur_text) and re.search(r"[A-Za-zÁ-ž]", nxt_text):
            return False

        # 3. aktuální nemá číslice a následující obsahuje číslice
        if not re.search(r"\d", cur_text) and re.search(r"\d", nxt_text):
            return False

        return True

    @staticmethod
    def merge_words(words: list[dict], max_gap: int = 20) -> list[dict]:
        merged = []
        if not words:
            return merged

        words_sorted = sorted(words, key=lambda w: (w["bbox"][1], w["bbox"][0]))
        current = words_sorted[0]

        for nxt in words_sorted[1:]:
            gap = nxt["bbox"][0] - current["bbox"][2]

            if gap <= max_gap and OcrPipeline._should_merge_words(current["text"], nxt["text"]):
                # spojíme
                current = {
                    "text": current["text"] + " " + nxt["text"],
                    "bbox": (
                        current["bbox"][0],
                        min(current["bbox"][1], nxt["bbox"][1]),
                        nxt["bbox"][2],
                        max(current["bbox"][3], nxt["bbox"][3]),
                    )
                }
            else:
                merged.append(current)
                current = nxt

        merged.append(current)
        return merged
