from OCR.OcrPipeline import OcrPipeline


def process(file_path):
    """
    End-to-end zpracování PDF dokumentu:
    - OCR pipeline (sanitization → merging → splitting → page marker)
    - Value klasifikace
    - Čištění textů

    :param file_path: Cesta k PDF souboru
    :return: DataFrame s kompletně zpracovanými tokeny po postprocessingu
    """
    pipeline = OcrPipeline()
    df = pipeline.load_image(file_path)

    return df