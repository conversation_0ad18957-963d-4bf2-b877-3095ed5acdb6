import pytesseract
from pytesseract import Output
import cv2
import numpy as np


class OcrEngine:
    def __init__(self, lang="ces", datapath=None):
        """
        Inicializace OCR enginu.
        :param lang: Jazyk (např. 'ces', 'eng').
        :param datapath: cesta k tessdata, None = default.
        """
        self.lang = lang
        self.datapath = datapath

        if datapath is not None:
            pytesseract.pytesseract.tesseract_cmd = datapath

    def process_text(self, image: np.ndarray) -> str:
        """
        Vrátí plain text z OpenCV obrazu (numpy array).
        """
        if image is None or not isinstance(image, np.ndarray):
            raise ValueError("Input must be a valid OpenCV image (numpy.ndarray).")

        text = pytesseract.image_to_string(image, lang=self.lang)
        return text

    def process_data(self, image: cv2.Mat):
        """
        Vrátí dataframe se strukturou blok/odstavec/řádek/slovo.
        """
        #if image is None or not isinstance(image, cv2.Mat):
        #    raise ValueError("Input must be a valid OpenCV image (numpy.ndarray).")

        df = pytesseract.image_to_data(image, lang=self.lang, output_type=Output.DATAFRAME)
        return df.dropna(subset=["text"]).reset_index(drop=True)

    def __del__(self):
        # v Pythonu není potřeba End(), ale v C++ ho zavoláš
        pass
