#!/usr/bin/env python3
"""
Test skript pro ověřen<PERSON> funkčnosti GATDataFrameClassifier.
"""

import pandas as pd
import os
import tempfile
import shutil
from classify_dataframe import GATDataFrameClassifier

def test_basic_functionality():
    """Test základní funkčnosti."""
    print("Test 1: Základní funkčnost")
    print("-" * 40)
    
    try:
        # Inicializace klasifikátoru
        classifier = GATDataFrameClassifier()
        print("✓ Klasifikátor úspěšně inicializován")
        
        # Test informací o třídách
        class_info = classifier.get_class_info()
        assert 'result_classes' in class_info
        assert class_info['max_result_class'] == 18
        print("✓ Informace o třídách v pořádku")
        
        return True
        
    except Exception as e:
        print(f"✗ Chyba: {e}")
        return False

def test_dataframe_classification():
    """Test klasifikace DataFrame."""
    print("\nTest 2: Klasifikace DataFrame")
    print("-" * 40)
    
    try:
        classifier = GATDataFrameClassifier()
        
        # Načtení testovacích dat
        test_file = '../Results/Pohoda01.csv'
        if not os.path.exists(test_file):
            print(f"✗ Testovací soubor nenalezen: {test_file}")
            return False
        
        df = pd.read_csv(test_file)
        print(f"✓ Načteno {len(df)} záznamů")
        
        # Klasifikace
        result_df, stats = classifier.classify(df)
        
        # Kontroly
        assert 'result_class' in result_df.columns
        assert len(result_df) == len(df)
        assert stats['classified_count'] + stats['unclassified_count'] == len(df)
        
        print(f"✓ Klasifikace dokončena:")
        print(f"  - Klasifikováno: {stats['classified_count']}")
        print(f"  - Neklasifikováno: {stats['unclassified_count']}")
        
        return True
        
    except Exception as e:
        print(f"✗ Chyba: {e}")
        return False

def test_confidence_threshold():
    """Test prahu spolehlivosti."""
    print("\nTest 3: Práh spolehlivosti")
    print("-" * 40)
    
    try:
        classifier = GATDataFrameClassifier()
        
        test_file = '../Results/Pohoda02.csv'
        if not os.path.exists(test_file):
            print(f"✗ Testovací soubor nenalezen: {test_file}")
            return False
        
        df = pd.read_csv(test_file)
        
        # Test různých prahů
        thresholds = [0.0, 0.5, 0.8]
        prev_classified = float('inf')
        
        for threshold in thresholds:
            result_df, stats = classifier.classify(df, confidence_threshold=threshold)
            classified = stats['classified_count']
            
            # S vyšším prahem by mělo být klasifikováno méně nebo stejně záznamů
            assert classified <= prev_classified
            prev_classified = classified
            
            print(f"✓ Práh {threshold}: {classified} klasifikováno")
        
        return True
        
    except Exception as e:
        print(f"✗ Chyba: {e}")
        return False

def test_file_classification():
    """Test klasifikace souboru."""
    print("\nTest 4: Klasifikace souboru")
    print("-" * 40)
    
    try:
        classifier = GATDataFrameClassifier()
        
        # Vytvoření dočasného souboru
        test_input = '../Results/Pohoda03.csv'
        if not os.path.exists(test_input):
            print(f"✗ Testovací soubor nenalezen: {test_input}")
            return False
        
        with tempfile.NamedTemporaryFile(mode='w', suffix='.csv', delete=False) as tmp:
            temp_output = tmp.name
        
        try:
            # Klasifikace souboru
            result_df, stats = classifier.classify_file(test_input, temp_output)
            
            # Kontrola výstupního souboru
            assert os.path.exists(temp_output)
            
            # Načtení a kontrola výsledků
            saved_df = pd.read_csv(temp_output)
            assert 'result_class' in saved_df.columns
            assert len(saved_df) == len(result_df)
            
            print(f"✓ Soubor úspěšně klasifikován a uložen")
            print(f"  - Klasifikováno: {stats['classified_count']}")
            
            return True
            
        finally:
            # Vyčištění
            if os.path.exists(temp_output):
                os.unlink(temp_output)
        
    except Exception as e:
        print(f"✗ Chyba: {e}")
        return False

def test_multiple_files():
    """Test klasifikace více souborů."""
    print("\nTest 5: Klasifikace více souborů")
    print("-" * 40)
    
    try:
        classifier = GATDataFrameClassifier()
        
        # Kontrola dostupnosti testovacích souborů
        test_files = ['Pohoda01.csv', 'Pohoda02.csv', 'Pohoda03.csv']
        available_files = []
        
        for filename in test_files:
            filepath = f'../Results/{filename}'
            if os.path.exists(filepath):
                available_files.append(filename)
        
        if len(available_files) < 2:
            print(f"✗ Nedostatek testovacích souborů (nalezeno {len(available_files)})")
            return False
        
        # Vytvoření dočasné složky
        with tempfile.TemporaryDirectory() as temp_dir:
            # Kopírování testovacích souborů
            temp_input_dir = os.path.join(temp_dir, 'input')
            temp_output_dir = os.path.join(temp_dir, 'output')
            os.makedirs(temp_input_dir)
            
            for filename in available_files[:2]:  # Použijeme pouze první 2 soubory
                src = f'../Results/{filename}'
                dst = os.path.join(temp_input_dir, filename)
                shutil.copy2(src, dst)
            
            # Klasifikace více souborů
            results = classifier.classify_multiple_files(
                input_dir=temp_input_dir,
                output_dir=temp_output_dir,
                confidence_threshold=0.5
            )
            
            # Kontroly
            assert len(results) == 2
            successful = sum(1 for r in results.values() if r['success'])
            assert successful == 2
            
            # Kontrola výstupních souborů
            for filename in available_files[:2]:
                output_file = os.path.join(temp_output_dir, filename)
                assert os.path.exists(output_file)
                
                df = pd.read_csv(output_file)
                assert 'result_class' in df.columns
            
            print(f"✓ Úspěšně zpracováno {successful} souborů")
            
            return True
        
    except Exception as e:
        print(f"✗ Chyba: {e}")
        return False

def test_error_handling():
    """Test zpracování chyb."""
    print("\nTest 6: Zpracování chyb")
    print("-" * 40)
    
    try:
        # Test neexistujícího modelu
        try:
            GATDataFrameClassifier(model_path='neexistujici_model.pth')
            print("✗ Měla by být vyvolána chyba pro neexistující model")
            return False
        except FileNotFoundError:
            print("✓ Správně zachycena chyba pro neexistující model")
        
        # Test neplatného DataFrame
        classifier = GATDataFrameClassifier()
        
        try:
            invalid_df = pd.DataFrame({'wrong_column': [1, 2, 3]})
            classifier.classify(invalid_df)
            print("✗ Měla by být vyvolána chyba pro neplatný DataFrame")
            return False
        except ValueError:
            print("✓ Správně zachycena chyba pro neplatný DataFrame")
        
        # Test neexistujícího souboru
        try:
            classifier.classify_file('neexistujici_soubor.csv')
            print("✗ Měla by být vyvolána chyba pro neexistující soubor")
            return False
        except FileNotFoundError:
            print("✓ Správně zachycena chyba pro neexistující soubor")
        
        return True
        
    except Exception as e:
        print(f"✗ Neočekávaná chyba: {e}")
        return False

def main():
    """Spuštění všech testů."""
    print("TESTOVÁNÍ GAT DATAFRAME CLASSIFIER")
    print("=" * 50)
    
    # Kontrola předpokladů
    if not os.path.exists('training_results/best_model.pth'):
        print("CHYBA: Model 'training_results/best_model.pth' nenalezen.")
        print("Ujistěte se, že máte natrénovaný model.")
        return
    
    if not os.path.exists('../Results'):
        print("CHYBA: Složka '../Results' nenalezena.")
        print("Ujistěte se, že máte k dispozici testovací data.")
        return
    
    # Spuštění testů
    tests = [
        test_basic_functionality,
        test_dataframe_classification,
        test_confidence_threshold,
        test_file_classification,
        test_multiple_files,
        test_error_handling
    ]
    
    passed = 0
    total = len(tests)
    
    for test_func in tests:
        try:
            if test_func():
                passed += 1
        except Exception as e:
            print(f"✗ Test {test_func.__name__} selhal s chybou: {e}")
    
    # Výsledky
    print("\n" + "=" * 50)
    print(f"VÝSLEDKY TESTŮ: {passed}/{total} prošlo")
    
    if passed == total:
        print("✓ Všechny testy prošly úspěšně!")
    else:
        print(f"✗ {total - passed} testů selhalo")
    
    print("=" * 50)

if __name__ == '__main__':
    main()
