import numpy as np
import pandas as pd
import torch
import torch.nn as nn
import torch.nn.functional as F
import matplotlib.pyplot as plt
import os
from sklearn.metrics import f1_score

class GATLayer(nn.Module):
    """
    GAT vrstva s podporou edge features
    """

    def __init__(self, node_in_dim, edge_in_dim, node_out_dim, heads=4, dropout_rate=0.5):
        super().__init__()
        self.heads = heads
        self.node_out_dim = node_out_dim

        # Lineární projekce
        self.linear_nodes = nn.Linear(node_in_dim, node_out_dim * heads, bias=False)
        self.linear_edges = nn.Linear(edge_in_dim, heads, bias=False)

        # Parametry pozornosti
        self.attn_l = nn.Parameter(torch.Tensor(1, heads, node_out_dim))
        self.attn_r = nn.Parameter(torch.Tensor(1, heads, node_out_dim))

        self.dropout = nn.Dropout(p=dropout_rate)
        self.reset_parameters()

    def reset_parameters(self):
        nn.init.xavier_uniform_(self.linear_nodes.weight)
        nn.init.xavier_uniform_(self.linear_edges.weight)
        nn.init.xavier_uniform_(self.attn_l)
        nn.init.xavier_uniform_(self.attn_r)

    def forward(self, node_features, edge_features, adj):
        B, N, _ = node_features.shape

        # 1. Lineární projekce
        Wh = self.linear_nodes(node_features).view(B, N, self.heads, self.node_out_dim)
        We = self.linear_edges(edge_features)  # [B, N, N, heads]

        # 2. Výpočet skóre pozornosti
        score_l = (Wh * self.attn_l).sum(dim=-1).unsqueeze(2)  # [B, N, 1, heads]
        score_r = (Wh * self.attn_r).sum(dim=-1).unsqueeze(1)  # [B, 1, N, heads]

        scores = score_l + score_r + We
        scores = F.leaky_relu(scores, negative_slope=0.2)

        # 3. Maskování a Softmax
        mask = (adj == 0).unsqueeze(-1)
        scores = scores.masked_fill(mask, -1e9)
        attn = torch.softmax(scores, dim=2)
        attn = self.dropout(attn)

        # 4. Agregace
        attn_permuted = attn.permute(0, 3, 1, 2)
        Wh_permuted = Wh.permute(0, 2, 1, 3)
        node_repr = torch.matmul(attn_permuted, Wh_permuted)
        node_repr = node_repr.permute(0, 2, 1, 3).contiguous().view(B, N, -1)

        return node_repr


class GatNodeClassifier(nn.Module):
    """
    Kompletní GAT klasifikátor uzlů
    """

    def __init__(self, node_in_dim, hidden_dim=64, heads=4, output_dim=18, num_layers=2, dropout_rate=0.5):
        super().__init__()
        self.layers = nn.ModuleList()
        self.dropout = nn.Dropout(dropout_rate)

        # První vrstva
        self.layers.append(GATLayer(
            node_in_dim,
            4,
            hidden_dim,
            heads,
            dropout_rate
        ))

        # Skryté vrstvy
        for _ in range(1, num_layers):
            self.layers.append(GATLayer(
                hidden_dim * heads,
                4,
                hidden_dim,
                heads,
                dropout_rate
            ))

        # Výstupní vrstva
        self.classifier = nn.Linear(hidden_dim * heads, output_dim)

    def forward(self, node_features, edge_features, adj):
        h = node_features
        for i, layer in enumerate(self.layers):
            h = layer(h, edge_features, adj)
            if i < len(self.layers) - 1:
                h = F.elu(h)
                h = self.dropout(h)
        return self.classifier(h)


def normalize_data(df, max_key_class, max_value_class):
    """
    Normalizuje data a přidává geometrické vlastnosti
    """
    # Kontrola prvního řádku (stránka)
    if df.iloc[0]['text'] != 'page' or df.iloc[0]['left'] != 0 or df.iloc[0]['top'] != 0:
        raise ValueError("První řádek musí být 'page' s left=0, top=0")

    page_width = df.iloc[0]['width']
    page_height = df.iloc[0]['height']

    # Výpočet středů
    df['cx'] = df['left'] + df['width'] / 2
    df['cy'] = df['top'] + df['height'] / 2

    # Normalizace souřadnic
    df['left_norm'] = df['left'] / page_width
    df['top_norm'] = df['top'] / page_height
    df['width_norm'] = df['width'] / page_width
    df['height_norm'] = df['height'] / page_height
    df['cx_norm'] = df['cx'] / page_width
    df['cy_norm'] = df['cy'] / page_height

    # One-hot encoding pro key_class a value_class
    def one_hot_encode(df, col_name, max_classes, prefix):
        for i in range(max_classes + 1):  # včetně 0
            df[f'{prefix}_{i}'] = (df[col_name] == i).astype(int)
        return df

    df = one_hot_encode(df, 'key_class', max_key_class, 'key_class')
    df = one_hot_encode(df, 'value_class', max_value_class, 'value_class')

    return df


def calculate_node_dim(max_key_class, max_value_class):
    """
    Vypočítá dimenzi vstupních vlastností uzlů
    """
    # 6 geometrických vlastností + one-hot pro key_class a value_class
    return 6 + (max_key_class + 1) + (max_value_class + 1)


def prepare_graph_data(df, device, max_key_class, max_value_class, max_result_class, is_training=True):
    """
    Připraví grafová data pro model
    """
    # Normalizace a přidání vlastností
    df = normalize_data(df, max_key_class, max_value_class)

    # Seznam sloupců pro vlastnosti uzlů
    coord_cols = ['left_norm', 'top_norm', 'width_norm', 'height_norm', 'cx_norm', 'cy_norm']
    key_cols = [col for col in df.columns if col.startswith('key_class_')]
    value_cols = [col for col in df.columns if col.startswith('value_class_')]
    node_feature_cols = coord_cols + key_cols + value_cols

    node_features_np = df[node_feature_cols].values.astype(np.float32)
    num_nodes = node_features_np.shape[0]

    # Výpočet vlastností hran
    cx_norm = df['cx_norm'].values
    cy_norm = df['cy_norm'].values

    dx = cx_norm[:, np.newaxis] - cx_norm[np.newaxis, :]
    dy = cy_norm[:, np.newaxis] - cy_norm[np.newaxis, :]
    dist = np.sqrt(dx ** 2 + dy ** 2 + 1e-8)  # +epsilon pro stabilitu
    angle = np.arctan2(dy, dx)

    edge_features_np = np.stack([dx, dy, dist, angle], axis=-1).astype(np.float32)

    # Matice sousednosti (plně propojená kromě self-loop)
    adj_np = np.ones((num_nodes, num_nodes), dtype=np.float32)
    np.fill_diagonal(adj_np, 0)

    # Příprava labelů a masky
    if 'result_class' in df.columns:
        labels_np = df['result_class'].values.astype(np.int64)
    else:
        labels_np = np.zeros(num_nodes, dtype=np.int64)

    if is_training:
        # Trénovací maska: pouze hodnotové uzly s platnou třídou (>0)
        train_mask_np = (df['value_class'] > 0) & (labels_np > 0)
    else:
        # Pro inferenci: všechny hodnotové uzly
        train_mask_np = (df['value_class'] > 0)

    # Konverze na tenzory
    node_features = torch.tensor(node_features_np).unsqueeze(0).to(device)
    edge_features = torch.tensor(edge_features_np).unsqueeze(0).to(device)
    adj = torch.tensor(adj_np).unsqueeze(0).to(device)
    labels = torch.tensor(labels_np).to(device)
    train_mask = torch.tensor(train_mask_np).to(device)

    return node_features, edge_features, adj, labels, train_mask

def train_model(model, train_files, val_files, device, max_key_class, max_value_class, max_result_class,
                epochs=100, lr=0.001, patience=5, output_dir="training_results"):
    """
    Rozšířená tréninková smyčka s vizualizací a early stopping
    """
    # Vytvoření výstupního adresáře
    os.makedirs(output_dir, exist_ok=True)

    #optimizer = torch.optim.Adam(model.parameters(), lr=lr)
    optimizer = torch.optim.Adam(model.parameters(),
                                lr=lr,
                                weight_decay=1e-4)  # L2 regularizace
    criterion = nn.CrossEntropyLoss()

    # Pro ukládání historie
    train_losses = []
    val_losses = []
    val_f1_scores = []
    best_f1 = 0.0
    epochs_no_improve = 0

    for epoch in range(epochs):
        # Tréninková fáze
        model.train()
        epoch_train_loss = 0.0
        train_batches = 0

        for file in train_files:
            df = pd.read_csv(file)
            node_feats, edge_feats, adj, labels, train_mask = prepare_graph_data(
                df, device, max_key_class, max_value_class, max_result_class, is_training=True
            )

            # Forward pass
            optimizer.zero_grad()
            logits = model(node_feats, edge_feats, adj).squeeze(0)

            if train_mask.sum() > 0:
                loss = criterion(logits[train_mask], labels[train_mask] - 1)  # Mapování 1-18 -> 0-17
                loss.backward()
                optimizer.step()

                epoch_train_loss += loss.item()
                train_batches += 1

        # Průměrná tréninková loss pro epochu
        avg_train_loss = epoch_train_loss / train_batches if train_batches > 0 else 0
        train_losses.append(avg_train_loss)

        # Validační fáze
        model.eval()
        epoch_val_loss = 0.0
        all_preds = []
        all_labels = []
        val_batches = 0

        for file in val_files:
            df = pd.read_csv(file)
            node_feats, edge_feats, adj, labels, val_mask = prepare_graph_data(
                df, device, max_key_class, max_value_class, max_result_class, is_training=True
            )

            with torch.no_grad():
                logits = model(node_feats, edge_feats, adj).squeeze(0)

                if val_mask.sum() > 0:
                    loss = criterion(logits[val_mask], labels[val_mask] - 1)
                    epoch_val_loss += loss.item()

                    # Predikce pro metriky
                    preds = torch.argmax(logits[val_mask], dim=1)
                    all_preds.extend(preds.cpu().numpy())
                    all_labels.extend((labels[val_mask] - 1).cpu().numpy())  # Mapování 1-18 -> 0-17

                    val_batches += 1

        # Průměrná validační loss
        avg_val_loss = epoch_val_loss / val_batches if val_batches > 0 else 0
        val_losses.append(avg_val_loss)

        # Výpočet F1 skóre
        if all_preds and all_labels:
            f1 = f1_score(all_labels, all_preds, average='weighted')
            val_f1_scores.append(f1)

            # Uložení nejlepšího modelu
            if f1 > best_f1:
                best_f1 = f1
                torch.save(model.state_dict(), os.path.join(output_dir, 'best_model.pth'))
                epochs_no_improve = 0
            else:
                epochs_no_improve += 1
        else:
            f1 = 0.0
            val_f1_scores.append(f1)

        # Výpis statistik
        print(f'Epoch {epoch + 1}/{epochs}: '
              f'Train Loss: {avg_train_loss:.4f}, '
              f'Val Loss: {avg_val_loss:.4f}, '
              f'Val F1: {f1:.4f}')

        # Early stopping
        if epochs_no_improve >= patience:
            print(f'Early stopping after {epoch + 1} epochs, no improvement for {patience} epochs')
            break

    # Vizualizace průběhu učení
    plt.figure(figsize=(12, 5))

    # Graf loss
    plt.subplot(1, 2, 1)
    plt.plot(train_losses, label='Train Loss')
    plt.plot(val_losses, label='Val Loss')
    plt.xlabel('Epoch')
    plt.ylabel('Loss')
    plt.title('Training and Validation Loss')
    plt.legend()
    plt.grid(True)

    # Graf F1 skóre
    plt.subplot(1, 2, 2)
    plt.plot(val_f1_scores, label='Val F1', color='green')
    plt.xlabel('Epoch')
    plt.ylabel('F1 Score')
    plt.title('Validation F1 Score')
    plt.ylim([0, 1])
    plt.legend()
    plt.grid(True)

    # Uložení grafu
    plt.tight_layout()
    plt.savefig(os.path.join(output_dir, 'training_metrics.png'))
    plt.close()

    # Uložení finálního modelu
    torch.save(model.state_dict(), os.path.join(output_dir, 'final_model.pth'))

    print(f'Training complete. Best validation F1: {best_f1:.4f}')

    return train_losses, val_losses, val_f1_scores


def predict(model, df, device, max_key_class, max_value_class, max_result_class):
    """
    Predikce pro nový dokument s informací o spolehlivosti klasifikace
    Vrací:
        predicted_classes: pole s predikovanými třídami (0 pro neklasifikované)
        confidences: pole s confidence scores (0.0-1.0)
    """
    # Příprava dat
    node_feats, edge_feats, adj, _, pred_mask = prepare_graph_data(
        df, device, max_key_class, max_value_class, max_result_class, is_training=False
    )

    # Predikce
    model.eval()
    with torch.no_grad():
        logits = model(node_feats, edge_feats, adj).squeeze(0)  # [N, output_dim]

        # Výpočet confidence
        probabilities = F.softmax(logits, dim=1)
        confidences, pred_indices = torch.max(probabilities, dim=1)
        pred_classes = pred_indices + 1  # Mapování 0-17 -> 1-18

    # Inicializace výsledků
    predicted_classes = np.zeros(len(df), dtype=int)
    confidence_scores = np.zeros(len(df), dtype=float)

    # Pouze pro hodnotové uzly
    mask_np = pred_mask.cpu().numpy()
    predicted_classes[mask_np] = pred_classes[pred_mask].cpu().numpy()
    confidence_scores[mask_np] = confidences[pred_mask].cpu().numpy()

    return predicted_classes, confidence_scores


# Konfigurace
if __name__ == "__main__":
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')

    # Maximální hodnoty tříd z datasetu
    MAX_KEY_CLASS = 26
    MAX_VALUE_CLASS = 8
    MAX_RESULT_CLASS = 18

    # Inicializace modelu
    node_in_dim = calculate_node_dim(MAX_KEY_CLASS, MAX_VALUE_CLASS)
    model = GatNodeClassifier(
        node_in_dim=node_in_dim,
        output_dim=MAX_RESULT_CLASS  # 18 výstupních tříd
    ).to(device)

    # Trénink
    train_files = ['Pohoda03.csv', 'Pohoda04.csv']  # Seznam tréninkových souborů
    train_model(model, train_files, device, MAX_KEY_CLASS, MAX_VALUE_CLASS, MAX_RESULT_CLASS)

    # Uložení modelu
    torch.save(model.state_dict(), 'gat_classifier.pth')

    # Predikce na novém dokumentu
    new_df = pd.read_csv('NewDocument.csv')
    predictions = predict(model, new_df, device, MAX_KEY_CLASS, MAX_VALUE_CLASS, MAX_RESULT_CLASS)
    new_df['predicted_class'] = predictions
    new_df.to_csv('NewDocument_with_predictions.csv', index=False)