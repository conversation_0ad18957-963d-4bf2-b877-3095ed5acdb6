# Skript pro vyhodnocení výsledků GAT klasifikační sítě

## Popis

Skript `evaluate_results.py` slouží k komplexnímu vyhodnocení výkonu GAT (Graph Attention Network) klasifikační sítě na všech souborech ze složky `../Results`. 

## Funkčnost

### Hlavní funkce:
1. **Načtení všech CSV souborů** ze složky `../Results`
2. **Klasifikace každého souboru** pomocí natrénovaného GAT modelu
3. **Porovnání predikovaných hodnot** s anotovanými daty ve sloupci `result_class`
4. **Filtrování pouze anotovaných záznamů** (result_class > 0)
5. **Vytvoření detailních statistik** a vizualizací

### Výstupní statistiky:

#### Pro jednotlivé soubory:
- Celkový počet záznamů
- Počet anotovaných záznamů
- Přesnost (Accuracy)
- Macro F1-Score
- Weighted F1-Score

#### Celkové statistiky:
- Celková přesnost napříč všemi soubory
- Precision, Recall, F1-Score pro každou třídu (1-18)
- Macro a Weighted průměry
- Matice záměn
- Support pro každou třídu

### Výstupní soubory:

Všechny výsledky se ukládají do složky `evaluation_results/`:

1. **`detailed_report.txt`** - Textový report se všemi statistikami včetně názvů tříd a analýzy spolehlivosti
2. **`file_statistics.csv`** - CSV se statistikami pro jednotlivé soubory včetně metrik spolehlivosti
3. **`class_statistics.csv`** - CSV se statistikami pro jednotlivé třídy včetně průměrné spolehlivosti
4. **`classification_errors.csv`** - CSV s analýzou nejčastějších chyb klasifikace
5. **`error_analysis.txt`** - Textový report s top 10 nejčastějšími chybami
6. **`accuracy_per_file.png`** - Graf přesnosti pro jednotlivé soubory
7. **`confusion_matrix.png`** - Matice záměn s názvy tříd
8. **`f1_score_per_class.png`** - F1-Score pro jednotlivé třídy s názvy
9. **`confidence_distribution.png`** - Histogram distribuce spolehlivosti
10. **`confidence_per_file.png`** - Analýza spolehlivosti pro jednotlivé soubory
11. **`predictions/`** - Složka s CSV soubory obsahujícími predikce pro každý vstupní soubor

## Použití

```bash
cd GAT_DS
python evaluate_results.py
```

## Požadavky

- Natrénovaný GAT model v `training_results/best_model.pth`
- CSV soubory s anotacemi ve složce `../Results`
- Všechny závislosti z `GATClassifier.py`

## Konfigurace

Konfigurační parametry lze upravit v sekci `CONFIG`:

```python
CONFIG = {
    'results_dir': '../Results',           # Složka s CSV soubory
    'model_path': 'training_results/best_model.pth',  # Cesta k modelu
    'output_dir': 'evaluation_results',    # Výstupní složka
    'confidence_threshold': 0.5            # Práh spolehlivosti pro klasifikaci
}
```

## Formát vstupních dat

CSV soubory musí obsahovat sloupce:
- `text` - Text elementu
- `left`, `top`, `width`, `height` - Souřadnice bounding boxu
- `value_class` - Třída hodnoty (pro určení, které elementy klasifikovat)
- `key_class` - Třída klíče
- `result_class` - Anotovaná třída (ground truth)
- `similarity` - Skóre podobnosti

## Výstup

Skript vypíše:
1. Průběh zpracování jednotlivých souborů
2. Tabulku se statistikami pro každý soubor
3. Detailní analýzu po třídách
4. Souhrnné statistiky

Příklad výstupu:
```
STATISTIKY PRO JEDNOTLIVÉ SOUBORY (práh spolehlivosti: 0.5):
------------------------------------------------------------------------------------------------------------------------
Soubor               Celkem   Anotace  Vysoká conf. Filtrováno  Accuracy   Orig. Acc. Avg Conf.
------------------------------------------------------------------------------------------------------------------------
Pohoda01.csv         32       5        4            1           0.800      0.600      0.723
Pohoda02.csv         28       4        3            1           0.750      0.500      0.681
...
CELKEM               1500     150      120          30          0.773      0.653      0.702

SOUHRNNÉ STATISTIKY SPOLEHLIVOSTI:
--------------------------------------------------------------------------------
Práh spolehlivosti: 0.5
Celkem anotovaných záznamů: 150
Záznamy s vysokou spolehlivostí: 120 (80.0%)
Odfiltrované záznamy: 30 (20.0%)
Průměrná spolehlivost: 0.702
Accuracy po filtrování: 0.773
Accuracy před filtrováním: 0.653
Rozdíl v accuracy: +0.120

DETAILNÍ ANALÝZA PO TŘÍDÁCH:
--------------------------------------------------------------------------------------------------------------
Třída    Název                          Precision    Recall       F1-Score     Support    Avg Conf.
--------------------------------------------------------------------------------------------------------------
1        Číslo faktury                  0.850        0.800        0.824        25         0.823
2        Datum vystavení                0.750        0.900        0.818        20         0.756
3        Datum splatnosti               0.900        0.850        0.874        18         0.789
...

Nejčastější chyby klasifikace:
--------------------------------------------------------------------------------
Skutečná třída            Predikovaná třída         Počet
--------------------------------------------------------------------------------
Datum vystavení           Datum splatnosti          3
IČO plátce               IČO dodavatele            2
...
```

## Poznámky

- Skript automaticky detekuje dostupné GPU/CPU
- Zpracovává pouze soubory s příponou `.csv`
- Ignoruje záznamy s `result_class = 0` (neanotované)
- Vytváří vizualizace ve vysokém rozlišení (300 DPI)
- **Používá číselníky z `utils/utils.py`** pro převod čísel tříd na slovní popisy
- Všechny statistiky a grafy obsahují jak číselné označení tříd, tak jejich názvy
- Analýza chyb pomáhá identifikovat, které třídy se nejčastěji zaměňují
