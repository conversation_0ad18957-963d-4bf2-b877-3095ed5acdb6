# GAT DataFrame Classifier

## Popis

Skript `classify_dataframe.py` poskytuje jednoduchou třídu `GATDataFrameClassifier` pro klasifikaci dat v pandas DataFrame pomocí natrénovaného GAT modelu. Klasifikované třídy uklád<PERSON> do sloupce `result_class`.

## Hlavní funkce

### Třída GATDataFrameClassifier

Hlavní třída pro klasifikaci dat s následujícími metodami:

#### `__init__(model_path, device=None)`
- Inicializuje klasifikátor s natrénovaným modelem
- Automaticky detekuje nejlepší dostupné zařízení (CUDA, MPS, CPU)

#### `classify(df, confidence_threshold=0.0, overwrite_existing=False)`
- Klasifikuje data v DataFrame
- Vrací DataFrame s přidaným sloupcem `result_class` a statistiky

#### `classify_file(input_path, output_path=None, ...)`
- Klasifikuje data ze CSV souboru
- Ukládá výsledky do souboru

#### `classify_multiple_files(input_dir, output_dir=None, ...)`
- Klasifikuje více souborů najednou
- Podporuje vzory pro názvy souborů

## Použití

### 1. Základní použití s DataFrame

```python
from classify_dataframe import GATDataFrameClassifier
import pandas as pd

# Inicializace klasifikátoru
classifier = GATDataFrameClassifier()

# Načtení dat
df = pd.read_csv('data.csv')

# Klasifikace
result_df, stats = classifier.classify(df)

# Zobrazení výsledků
print(f"Klasifikováno: {stats['classified_count']}")
print(f"Neklasifikováno: {stats['unclassified_count']}")

# Uložení výsledků
result_df.to_csv('classified_data.csv', index=False)
```

### 2. Klasifikace s prahem spolehlivosti

```python
# Klasifikace pouze záznamů s vysokou spolehlivostí
result_df, stats = classifier.classify(
    df, 
    confidence_threshold=0.8,  # Pouze záznamy s similarity >= 0.8
    overwrite_existing=True
)
```

### 3. Klasifikace souboru

```python
# Klasifikace jednoho souboru
result_df, stats = classifier.classify_file(
    input_path='input.csv',
    output_path='output.csv',
    confidence_threshold=0.7
)
```

### 4. Klasifikace více souborů

```python
# Klasifikace všech CSV souborů ve složce
results = classifier.classify_multiple_files(
    input_dir='../Results',
    output_dir='classified_results',
    file_pattern='*.csv',
    confidence_threshold=0.6
)

# Analýza výsledků
for filename, result in results.items():
    if result['success']:
        stats = result['stats']
        print(f"{filename}: {stats['classified_count']} klasifikováno")
```

### 5. Použití z příkazové řádky

```bash
# Klasifikace jednoho souboru
python classify_dataframe.py input.csv -o output.csv -t 0.8

# Klasifikace všech souborů ve složce
python classify_dataframe.py ../Results -o classified_results -t 0.7

# Použití specifického modelu a zařízení
python classify_dataframe.py input.csv -m my_model.pth --device cuda
```

## Parametry příkazové řádky

- `input` - Vstupní CSV soubor nebo složka
- `-o, --output` - Výstupní soubor nebo složka
- `-m, --model` - Cesta k modelu (výchozí: `training_results/best_model.pth`)
- `-t, --threshold` - Práh spolehlivosti 0.0-1.0 (výchozí: 0.0)
- `--overwrite` - Přepsat existující `result_class` sloupec
- `--device` - Zařízení pro výpočty (`cuda`, `cpu`, `mps`)

## Formát vstupních dat

CSV soubory musí obsahovat sloupce:
- `text` - Text elementu
- `left`, `top`, `width`, `height` - Souřadnice bounding boxu
- `value_class` - Třída hodnoty
- `key_class` - Třída klíče
- `similarity` - Skóre podobnosti

První řádek by měl být `page` s `left=0, top=0` a rozměry stránky.

## Výstupní data

Výstupní DataFrame obsahuje všechny původní sloupce plus:
- `result_class` - Klasifikovaná třída (0 = neklasifikováno, 1-18 = konkrétní třídy)

## Statistiky klasifikace

Metody vracejí slovník se statistikami:
```python
{
    'classified_count': 15,        # Počet klasifikovaných záznamů
    'unclassified_count': 5,       # Počet neklasifikovaných záznamů
    'class_distribution': {        # Distribuce tříd
        '1 - Číslo faktury': 3,
        '2 - Datum vystavení': 2,
        ...
    }
}
```

## Práh spolehlivosti

Parametr `confidence_threshold` umožňuje filtrovat klasifikace podle `similarity` skóre:
- `0.0` - Klasifikuje všechny záznamy (výchozí)
- `0.5` - Klasifikuje pouze záznamy s similarity >= 0.5
- `0.9` - Klasifikuje pouze záznamy s velmi vysokou spolehlivostí

## Zpracování chyb

Třída obsahuje robustní zpracování chyb:
- Validace vstupních dat
- Kontrola existence modelu
- Informativní chybové zprávy
- Graceful handling při zpracování více souborů

## Příklady použití

Spusťte `example_classify_usage.py` pro detailní příklady:

```bash
python example_classify_usage.py
```

Příklady zahrnují:
1. Základní použití
2. Použití s prahem spolehlivosti
3. Klasifikaci souborů
4. Klasifikaci více souborů
5. Pokročilou manipulaci s DataFrame
6. Zobrazení informací o třídách
7. Zpracování chyb

## Integrace s číselníky

Skript automaticky používá číselníky z `utils/utils.py`:
- `RESULT_CLASS_REGISTRY` - Názvy tříd pro result_class
- `get_result_class_name()` - Funkce pro převod ID na název

## Výhody oproti původnímu test.py

1. **Objektově orientovaný přístup** - Snadnější použití a rozšiřování
2. **Flexibilní vstup** - DataFrame, soubor nebo více souborů
3. **Práh spolehlivosti** - Kontrola kvality klasifikace
4. **Robustní zpracování chyb** - Informativní chybové zprávy
5. **Statistiky** - Detailní informace o výsledcích
6. **Příkazová řádka** - Snadné použití ze skriptů
7. **Dokumentace a příklady** - Kompletní dokumentace s příklady

## Požadavky

- Natrénovaný GAT model v `training_results/best_model.pth`
- Všechny závislosti z `GATClassifier.py`
- Číselníky v `utils/utils.py`

## Poznámky

- Skript automaticky detekuje nejlepší dostupné zařízení
- Podporuje Apple Silicon (MPS) pro rychlejší výpočty na Mac
- Vytváří kopii DataFrame pro bezpečnost
- Umožňuje přepsání existujících klasifikací
- Poskytuje detailní statistiky a analýzy
