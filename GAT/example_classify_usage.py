#!/usr/bin/env python3
"""
Příklady použití GATDataFrameClassifier pro klasifikaci dat v pandas DataFrame.
"""

import pandas as pd
import os
from classify_dataframe import GATDataFrameClassifier

def example_basic_usage():
    """Základní použití klasifikátoru."""
    print("=" * 60)
    print("PŘÍKLAD 1: Základní použití")
    print("=" * 60)
    
    # Inicializace klasifikátoru
    classifier = GATDataFrameClassifier()
    
    # Načtení dat
    df = pd.read_csv('../Results/Pohoda01.csv')
    print(f"Načteno {len(df)} záznamů")
    
    # Klasifikace
    result_df, stats = classifier.classify(df)
    
    # Zobrazení výsledků
    print(f"\nVýsledky klasifikace:")
    print(f"Klasifikováno: {stats['classified_count']}")
    print(f"Neklasifikováno: {stats['unclassified_count']}")
    
    print(f"\nDistribuce tříd:")
    for class_info, count in stats['class_distribution'].items():
        print(f"  {class_info}: {count}")
    
    # Zobrazení klasifikovaných záznamů
    classified_records = result_df[result_df['result_class'] > 0]
    if len(classified_records) > 0:
        print(f"\nPrvních 5 klasifikovaných záznamů:")
        print(classified_records[['text', 'result_class']].head())
    
    return result_df

def example_with_confidence_threshold():
    """Použití s prahem spolehlivosti."""
    print("\n" + "=" * 60)
    print("PŘÍKLAD 2: Použití s prahem spolehlivosti")
    print("=" * 60)
    
    classifier = GATDataFrameClassifier()
    df = pd.read_csv('../Results/Pohoda02.csv')
    
    # Klasifikace s různými prahy
    thresholds = [0.0, 0.5, 0.8, 0.9]
    
    for threshold in thresholds:
        result_df, stats = classifier.classify(df, confidence_threshold=threshold)
        print(f"Práh {threshold}: Klasifikováno {stats['classified_count']}, "
              f"Neklasifikováno {stats['unclassified_count']}")

def example_file_classification():
    """Klasifikace souboru."""
    print("\n" + "=" * 60)
    print("PŘÍKLAD 3: Klasifikace souboru")
    print("=" * 60)
    
    classifier = GATDataFrameClassifier()
    
    # Klasifikace jednoho souboru
    input_file = '../Results/Pohoda03.csv'
    output_file = 'classified_Pohoda03.csv'
    
    result_df, stats = classifier.classify_file(
        input_file, 
        output_file, 
        confidence_threshold=0.7
    )
    
    print(f"Soubor uložen jako: {output_file}")

def example_multiple_files():
    """Klasifikace více souborů."""
    print("\n" + "=" * 60)
    print("PŘÍKLAD 4: Klasifikace více souborů")
    print("=" * 60)
    
    classifier = GATDataFrameClassifier()
    
    # Vytvoření výstupní složky
    output_dir = 'classified_results'
    os.makedirs(output_dir, exist_ok=True)
    
    # Klasifikace všech souborů ze složky Results
    results = classifier.classify_multiple_files(
        input_dir='../Results',
        output_dir=output_dir,
        file_pattern='Pohoda0[1-5].csv',  # Pouze první 5 souborů
        confidence_threshold=0.6
    )
    
    # Analýza výsledků
    successful_files = [name for name, result in results.items() if result['success']]
    failed_files = [name for name, result in results.items() if not result['success']]
    
    print(f"\nÚspěšně zpracované soubory: {len(successful_files)}")
    for filename in successful_files:
        stats = results[filename]['stats']
        print(f"  {filename}: {stats['classified_count']} klasifikováno")
    
    if failed_files:
        print(f"\nChyby při zpracování: {len(failed_files)}")
        for filename in failed_files:
            print(f"  {filename}: {results[filename]['error']}")

def example_dataframe_manipulation():
    """Pokročilá manipulace s DataFrame."""
    print("\n" + "=" * 60)
    print("PŘÍKLAD 5: Pokročilá manipulace s DataFrame")
    print("=" * 60)
    
    classifier = GATDataFrameClassifier()
    
    # Načtení a spojení více souborů
    dfs = []
    for i in range(1, 4):
        df = pd.read_csv(f'../Results/Pohoda0{i}.csv')
        df['source_file'] = f'Pohoda0{i}.csv'
        dfs.append(df)
    
    combined_df = pd.concat(dfs, ignore_index=True)
    print(f"Spojeno {len(combined_df)} záznamů ze {len(dfs)} souborů")
    
    # Klasifikace spojeného DataFrame
    result_df, stats = classifier.classify(combined_df, confidence_threshold=0.5)
    
    # Analýza podle zdrojových souborů
    print(f"\nAnalýza podle zdrojových souborů:")
    for source_file in result_df['source_file'].unique():
        file_data = result_df[result_df['source_file'] == source_file]
        classified_count = (file_data['result_class'] > 0).sum()
        print(f"  {source_file}: {classified_count} klasifikováno z {len(file_data)}")
    
    # Export výsledků s dodatečnými informacemi
    output_file = 'combined_classified_results.csv'
    result_df.to_csv(output_file, index=False)
    print(f"\nVýsledky uloženy do: {output_file}")
    
    return result_df

def example_class_info():
    """Zobrazení informací o třídách."""
    print("\n" + "=" * 60)
    print("PŘÍKLAD 6: Informace o třídách")
    print("=" * 60)
    
    classifier = GATDataFrameClassifier()
    class_info = classifier.get_class_info()
    
    print("Dostupné result_class třídy:")
    for class_id, class_name in class_info['result_classes'].items():
        print(f"  {class_id}: {class_name}")
    
    print(f"\nParametry modelu:")
    print(f"  Max key_class: {class_info['max_key_class']}")
    print(f"  Max value_class: {class_info['max_value_class']}")
    print(f"  Max result_class: {class_info['max_result_class']}")

def example_error_handling():
    """Příklad zpracování chyb."""
    print("\n" + "=" * 60)
    print("PŘÍKLAD 7: Zpracování chyb")
    print("=" * 60)
    
    try:
        # Pokus o načtení neexistujícího modelu
        classifier = GATDataFrameClassifier(model_path='neexistujici_model.pth')
    except FileNotFoundError as e:
        print(f"Očekávaná chyba - model nenalezen: {e}")
    
    # Správná inicializace
    classifier = GATDataFrameClassifier()
    
    try:
        # Pokus o klasifikaci neplatného DataFrame
        invalid_df = pd.DataFrame({'wrong_column': [1, 2, 3]})
        classifier.classify(invalid_df)
    except ValueError as e:
        print(f"Očekávaná chyba - neplatný DataFrame: {e}")
    
    try:
        # Pokus o klasifikaci neexistujícího souboru
        classifier.classify_file('neexistujici_soubor.csv')
    except FileNotFoundError as e:
        print(f"Očekávaná chyba - soubor nenalezen: {e}")

def main():
    """Spuštění všech příkladů."""
    print("PŘÍKLADY POUŽITÍ GAT DATAFRAME CLASSIFIER")
    print("=" * 80)
    
    # Kontrola dostupnosti testovacích dat
    if not os.path.exists('../Results'):
        print("CHYBA: Složka '../Results' nenalezena.")
        print("Ujistěte se, že máte k dispozici testovací data.")
        return
    
    # Kontrola dostupnosti modelu
    if not os.path.exists('training_results/best_model.pth'):
        print("CHYBA: Model 'training_results/best_model.pth' nenalezen.")
        print("Ujistěte se, že máte natrénovaný model.")
        return
    
    try:
        # Spuštění příkladů
        example_basic_usage()
        example_with_confidence_threshold()
        example_file_classification()
        example_multiple_files()
        example_dataframe_manipulation()
        example_class_info()
        example_error_handling()
        
        print("\n" + "=" * 80)
        print("VŠECHNY PŘÍKLADY DOKONČENY")
        print("=" * 80)
        
    except Exception as e:
        print(f"CHYBA při spouštění příkladů: {e}")
        import traceback
        traceback.print_exc()

if __name__ == '__main__':
    main()
