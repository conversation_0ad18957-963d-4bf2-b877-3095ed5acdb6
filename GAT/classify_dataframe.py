import os
import pandas as pd
import torch
import numpy as np
from typing import Optional, Union
import warnings

from GATClassifier import GatNodeClassifier, calculate_node_dim, predict
import sys
sys.path.append('../utils')
from utils import RESULT_CLASS_REGISTRY, get_result_class_name

class GATDataFrameClassifier:
    """
    Třída pro klasifikaci dat v pandas DataFrame pomocí GAT modelu.
    Klasifikované třídy ukládá do sloupce result_class.
    """
    
    def __init__(self, model_path='training_results/best_model.pth', device=None):
        """
        Inicializace klasifikátoru.
        
        Args:
            model_path (str): Cesta k natrénovanému modelu
            device (str, optional): Zařízení pro výpočty ('cuda', 'cpu', 'mps'). 
                                   Pokud None, automaticky detekuje nejlepší dostupné.
        """
        # Konstanty modelu
        self.MAX_KEY_CLASS = 26
        self.MAX_VALUE_CLASS = 8
        self.MAX_RESULT_CLASS = 18
        
        # Nastavení zařízení
        if device is None:
            if torch.cuda.is_available():
                self.device = torch.device('cuda')
            elif hasattr(torch.backends, 'mps') and torch.backends.mps.is_available():
                self.device = torch.device('mps')
            else:
                self.device = torch.device('cpu')
        else:
            self.device = torch.device(device)
        
        print(f"Používané zařízení: {self.device}")
        
        # Inicializace modelu
        self.model = self._load_model(model_path)
        
    def _load_model(self, model_path):
        """Načte natrénovaný model."""
        if not os.path.exists(model_path):
            raise FileNotFoundError(f"Model nenalezen na cestě: {model_path}")
        
        # Inicializace modelu
        node_in_dim = calculate_node_dim(self.MAX_KEY_CLASS, self.MAX_VALUE_CLASS)
        model = GatNodeClassifier(
            node_in_dim=node_in_dim,
            output_dim=self.MAX_RESULT_CLASS
        ).to(self.device)
        
        # Načtení vah
        try:
            model.load_state_dict(torch.load(model_path, map_location=self.device))
            model.eval()
            print(f"Model úspěšně načten z: {model_path}")
            return model
        except Exception as e:
            raise RuntimeError(f"Chyba při načítání modelu: {e}")
    
    def _validate_dataframe(self, df):
        """Validuje vstupní DataFrame."""
        required_columns = ['text', 'left', 'top', 'width', 'height', 'value_class', 'key_class', 'similarity']
        missing_columns = [col for col in required_columns if col not in df.columns]
        
        if missing_columns:
            raise ValueError(f"DataFrame neobsahuje požadované sloupce: {missing_columns}")
        
        # Kontrola prvního řádku (stránka)
        if len(df) == 0:
            raise ValueError("DataFrame je prázdný")
        
        if df.iloc[0]['text'] != 'page' or df.iloc[0]['left'] != 0 or df.iloc[0]['top'] != 0:
            warnings.warn("První řádek neodpovídá očekávanému formátu 'page' s left=0, top=0")
        
        return True
    
    def classify(self, df, confidence_threshold=0.0, overwrite_existing=False):
        """
        Klasifikuje data v DataFrame a uloží výsledky do sloupce result_class.
        
        Args:
            df (pd.DataFrame): DataFrame s daty k klasifikaci
            confidence_threshold (float): Minimální práh spolehlivosti pro klasifikaci (0.0-1.0)
            overwrite_existing (bool): Zda přepsat existující result_class sloupec
            
        Returns:
            pd.DataFrame: DataFrame s přidaným/aktualizovaným sloupcem result_class
            dict: Statistiky klasifikace
        """
        # Validace vstupních dat
        self._validate_dataframe(df)
        
        # Kopie DataFrame pro bezpečnost
        result_df = df.copy()
        
        # Kontrola existujícího sloupce result_class
        if 'result_class' in result_df.columns and not overwrite_existing:
            print("Sloupec 'result_class' již existuje. Použijte overwrite_existing=True pro přepsání.")
            return result_df, self._get_classification_stats(result_df)
        
        # Provedení klasifikace
        try:
            predictions = predict(
                self.model, 
                result_df, 
                self.device, 
                self.MAX_KEY_CLASS, 
                self.MAX_VALUE_CLASS, 
                self.MAX_RESULT_CLASS
            )
            
            # Aplikace prahu spolehlivosti (pokud je zadán)
            if confidence_threshold > 0.0:
                # Filtrujeme predikce podle similarity score
                low_confidence_mask = result_df['similarity'] < confidence_threshold
                predictions[low_confidence_mask] = 0
            
            # Uložení výsledků
            result_df['result_class'] = predictions
            
            # Vytvoření statistik
            stats = self._get_classification_stats(result_df)
            
            print(f"Klasifikace dokončena:")
            print(f"  - Celkem záznamů: {len(result_df)}")
            print(f"  - Klasifikováno: {stats['classified_count']}")
            print(f"  - Neklasifikováno: {stats['unclassified_count']}")
            
            return result_df, stats
            
        except Exception as e:
            raise RuntimeError(f"Chyba při klasifikaci: {e}")
    
    def _get_classification_stats(self, df):
        """Vytvoří statistiky klasifikace."""
        if 'result_class' not in df.columns:
            return {'classified_count': 0, 'unclassified_count': len(df), 'class_distribution': {}}
        
        classified_count = (df['result_class'] > 0).sum()
        unclassified_count = (df['result_class'] == 0).sum()
        
        # Distribuce tříd
        class_counts = df[df['result_class'] > 0]['result_class'].value_counts().sort_index()
        class_distribution = {}
        for class_id, count in class_counts.items():
            class_name = get_result_class_name(class_id)
            class_distribution[f"{class_id} - {class_name}"] = count
        
        return {
            'classified_count': classified_count,
            'unclassified_count': unclassified_count,
            'class_distribution': class_distribution
        }
    
    def classify_file(self, input_path, output_path=None, confidence_threshold=0.0, overwrite_existing=False):
        """
        Klasifikuje data ze souboru a uloží výsledky.
        
        Args:
            input_path (str): Cesta ke vstupnímu CSV souboru
            output_path (str, optional): Cesta k výstupnímu souboru. Pokud None, přepíše vstupní soubor.
            confidence_threshold (float): Minimální práh spolehlivosti
            overwrite_existing (bool): Zda přepsat existující result_class sloupec
            
        Returns:
            pd.DataFrame: DataFrame s výsledky klasifikace
            dict: Statistiky klasifikace
        """
        if not os.path.exists(input_path):
            raise FileNotFoundError(f"Vstupní soubor nenalezen: {input_path}")
        
        # Načtení dat
        df = pd.read_csv(input_path)
        print(f"Načten soubor: {input_path} ({len(df)} záznamů)")
        
        # Klasifikace
        result_df, stats = self.classify(df, confidence_threshold, overwrite_existing)
        
        # Uložení výsledků
        if output_path is None:
            output_path = input_path
        
        result_df.to_csv(output_path, index=False)
        print(f"Výsledky uloženy do: {output_path}")
        
        return result_df, stats
    
    def classify_multiple_files(self, input_dir, output_dir=None, file_pattern="*.csv", 
                              confidence_threshold=0.0, overwrite_existing=False):
        """
        Klasifikuje více souborů najednou.
        
        Args:
            input_dir (str): Složka se vstupními soubory
            output_dir (str, optional): Výstupní složka. Pokud None, přepíše vstupní soubory.
            file_pattern (str): Vzor pro názvy souborů
            confidence_threshold (float): Minimální práh spolehlivosti
            overwrite_existing (bool): Zda přepsat existující result_class sloupce
            
        Returns:
            dict: Slovník s výsledky pro každý soubor
        """
        import glob
        
        if not os.path.exists(input_dir):
            raise FileNotFoundError(f"Vstupní složka nenalezena: {input_dir}")
        
        # Vytvoření výstupní složky
        if output_dir and not os.path.exists(output_dir):
            os.makedirs(output_dir)
        
        # Najdeme všechny soubory
        search_pattern = os.path.join(input_dir, file_pattern)
        files = glob.glob(search_pattern)
        
        if not files:
            print(f"Žádné soubory nenalezeny podle vzoru: {search_pattern}")
            return {}
        
        results = {}
        print(f"Nalezeno {len(files)} souborů k zpracování")
        
        for i, input_path in enumerate(sorted(files), 1):
            filename = os.path.basename(input_path)
            print(f"\n[{i}/{len(files)}] Zpracovávám: {filename}")
            
            try:
                # Určení výstupní cesty
                if output_dir:
                    output_path = os.path.join(output_dir, filename)
                else:
                    output_path = input_path
                
                # Klasifikace
                result_df, stats = self.classify_file(
                    input_path, output_path, confidence_threshold, overwrite_existing
                )
                
                results[filename] = {
                    'success': True,
                    'dataframe': result_df,
                    'stats': stats,
                    'input_path': input_path,
                    'output_path': output_path
                }
                
            except Exception as e:
                print(f"  -> CHYBA: {e}")
                results[filename] = {
                    'success': False,
                    'error': str(e),
                    'input_path': input_path
                }
        
        # Souhrnné statistiky
        successful = sum(1 for r in results.values() if r['success'])
        failed = len(results) - successful
        
        print(f"\nSOUHRN:")
        print(f"Úspěšně zpracováno: {successful}")
        print(f"Chyby: {failed}")
        
        return results
    
    def get_class_info(self):
        """Vrátí informace o dostupných třídách."""
        return {
            'result_classes': RESULT_CLASS_REGISTRY,
            'max_key_class': self.MAX_KEY_CLASS,
            'max_value_class': self.MAX_VALUE_CLASS,
            'max_result_class': self.MAX_RESULT_CLASS
        }


def main():
    """Příklad použití klasifikátoru."""
    import argparse
    
    parser = argparse.ArgumentParser(description='GAT DataFrame Classifier')
    parser.add_argument('input', help='Vstupní CSV soubor nebo složka')
    parser.add_argument('-o', '--output', help='Výstupní soubor nebo složka')
    parser.add_argument('-m', '--model', default='training_results/best_model.pth', 
                       help='Cesta k modelu')
    parser.add_argument('-t', '--threshold', type=float, default=0.0,
                       help='Práh spolehlivosti (0.0-1.0)')
    parser.add_argument('--overwrite', action='store_true',
                       help='Přepsat existující result_class sloupec')
    parser.add_argument('--device', choices=['cuda', 'cpu', 'mps'],
                       help='Zařízení pro výpočty')
    
    args = parser.parse_args()
    
    # Inicializace klasifikátoru
    classifier = GATDataFrameClassifier(args.model, args.device)
    
    # Klasifikace
    if os.path.isfile(args.input):
        # Jeden soubor
        result_df, stats = classifier.classify_file(
            args.input, args.output, args.threshold, args.overwrite
        )
        
        print("\nDistribuce tříd:")
        for class_info, count in stats['class_distribution'].items():
            print(f"  {class_info}: {count}")
            
    elif os.path.isdir(args.input):
        # Více souborů
        results = classifier.classify_multiple_files(
            args.input, args.output, confidence_threshold=args.threshold,
            overwrite_existing=args.overwrite
        )
        
        # Souhrnné statistiky
        total_classified = 0
        total_unclassified = 0
        
        for filename, result in results.items():
            if result['success']:
                stats = result['stats']
                total_classified += stats['classified_count']
                total_unclassified += stats['unclassified_count']
        
        print(f"\nCelkové statistiky:")
        print(f"Klasifikováno: {total_classified}")
        print(f"Neklasifikováno: {total_unclassified}")
        
    else:
        print(f"CHYBA: Vstup '{args.input}' není platný soubor ani složka")


if __name__ == '__main__':
    main()
