import os
import pandas as pd
import torch
import numpy as np

from GATClassifier import GatNodeClassifier, calculate_node_dim, predict

def get_device(device=None):
    if device is None:
        if torch.cuda.is_available():
            device = torch.device('cuda')
        elif hasattr(torch.backends, 'mps') and torch.backends.mps.is_available():
            device = torch.device('mps')
        else:
            device = torch.device('cpu')
    else:
        device = torch.device(device)
    return device

CONFIG = {
    'results_dir': '../Results',
    'epochs': 50,
    'learning_rate': 0.001,
    'patience': 10,
    'device': get_device(),
    'model_path': 'gat_model.pt',
    'confidence_threshold': 0.5,  # Minimální spolehlivost pro klasifikaci
}

def load_files_from_results(dir_path):
    data = []
    for filename in os.listdir(dir_path):
        if filename.endswith('.csv'):
            data.append(os.path.join(dir_path, filename))
    return data

if __name__ == '__main__':
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')

    # Maximální hodnoty tříd z datasetu
    MAX_KEY_CLASS = 26
    MAX_VALUE_CLASS = 8
    MAX_RESULT_CLASS = 18

    # Inicializace modelu
    node_in_dim = calculate_node_dim(MAX_KEY_CLASS, MAX_VALUE_CLASS)
    model = GatNodeClassifier(
        node_in_dim=node_in_dim,
        output_dim=MAX_RESULT_CLASS  # 18 výstupních tříd
    ).to(device)

    model.load_state_dict(torch.load('training_results/best_model.pth', map_location=device))

    # Predikce na novém dokumentu
    new_df = pd.read_csv('../Results/Pohoda07.csv')
    pred_classes, confidences = predict(model, new_df, device, MAX_KEY_CLASS, MAX_VALUE_CLASS, MAX_RESULT_CLASS)

    # Aplikace prahu spolehlivosti z konfigurace
    confidence_threshold = CONFIG['confidence_threshold']

    # Vytvoření kopií pro úpravu
    filtered_pred_classes = pred_classes.copy()

    # Nastavení třídy na 0 (neklasifikováno) pro záznamy s nízkou spolehlivostí
    low_confidence_mask = confidences < confidence_threshold
    filtered_pred_classes[low_confidence_mask] = 0

    # Přiřazení výsledků do DataFrame
    new_df['predicted_class'] = filtered_pred_classes
    new_df['confidence'] = confidences

    # Statistiky klasifikace
    total_records = len(new_df)
    classified_records = (filtered_pred_classes > 0).sum()
    high_confidence_records = (confidences >= confidence_threshold).sum()

    print(f"Statistiky klasifikace:")
    print(f"  - Celkem záznamů: {total_records}")
    print(f"  - Záznamy s confidence >= {confidence_threshold}: {high_confidence_records}")
    print(f"  - Klasifikováno (po aplikaci prahu): {classified_records}")
    print(f"  - Neklasifikováno: {total_records - classified_records}")

    # Detailní analýza podle tříd (pouze klasifikované záznamy)
    if classified_records > 0:
        print(f"\nRozložení klasifikovaných záznamů podle tříd:")
        classified_mask = filtered_pred_classes > 0
        classified_classes = filtered_pred_classes[classified_mask]
        classified_confidences = confidences[classified_mask]

        # Import pro názvy tříd
        import sys
        sys.path.append('../utils')
        from utils import get_result_class_name

        # Statistiky pro každou třídu
        unique_classes = sorted(set(classified_classes))
        for class_id in unique_classes:
            class_mask = classified_classes == class_id
            class_count = class_mask.sum()
            avg_confidence = classified_confidences[class_mask].mean()
            class_name = get_result_class_name(class_id)

            print(f"  - Třída {class_id} ({class_name}): {class_count} záznamů, "
                  f"průměrná confidence: {avg_confidence:.3f}")

    # Analýza záznamů s nízkou spolehlivostí
    low_confidence_count = low_confidence_mask.sum()
    if low_confidence_count > 0:
        print(f"\nZáznamy s nízkou spolehlivostí (< {confidence_threshold}):")
        print(f"  - Celkem: {low_confidence_count} záznamů")

        # Zobrazení prvních 5 záznamů s nízkou spolehlivostí
        low_conf_indices = np.where(low_confidence_mask)[0]
        print(f"  - Prvních 5 záznamů s nízkou spolehlivostí:")
        for i, idx in enumerate(low_conf_indices[:5]):
            text = new_df.iloc[idx]['text'][:50] + "..." if len(new_df.iloc[idx]['text']) > 50 else new_df.iloc[idx]['text']
            original_class = pred_classes[idx]
            confidence = confidences[idx]
            class_name = get_result_class_name(original_class) if original_class > 0 else "Neklasifikováno"
            print(f"    {i+1}. '{text}' -> Třída {original_class} ({class_name}), confidence: {confidence:.3f}")

    new_df.to_csv('NewDocument_with_predictions.csv', index=False)
    print(f"\nVýsledky uloženy do: NewDocument_with_predictions.csv")