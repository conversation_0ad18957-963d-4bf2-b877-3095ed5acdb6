# Návrh trénovacího workflow, kde se ka<PERSON><PERSON><PERSON> graf p<PERSON><PERSON><PERSON> (tj. prepare_graph_data_from_dataframe volána jednou pro CSV)

import os
import pandas as pd
import torch
from sklearn.model_selection import train_test_split
import matplotlib.pyplot as plt

from GATClassifier import GatNodeClassifier, calculate_node_dim, train_model


def get_device(device=None):
    if device is None:
        if torch.cuda.is_available():
            device = torch.device('cuda')
        elif hasattr(torch.backends, 'mps') and torch.backends.mps.is_available():
            device = torch.device('mps')
        else:
            device = torch.device('cpu')
    else:
        device = torch.device(device)
    return device

CONFIG = {
    'results_dir': '../Results',
    'epochs': 100,
    'learning_rate': 0.0005,
    'patience': 15,
    'device': get_device(),
    'model_path': 'gat_model.pt',
}

def load_files_from_results(dir_path):
    data = []
    for filename in os.listdir(dir_path):
        if filename.endswith('.csv'):
            data.append(os.path.join(dir_path, filename))
            #data[filename] = os.path.join(dir_path, filename)
    return data


if __name__ == '__main__':
    device = CONFIG['device']

    # Maximální hodnoty tříd z datasetu
    MAX_KEY_CLASS = 26
    MAX_VALUE_CLASS = 8
    MAX_RESULT_CLASS = 18

    # Inicializace modelu
    node_in_dim = calculate_node_dim(MAX_KEY_CLASS, MAX_VALUE_CLASS)
    model = GatNodeClassifier(
        node_in_dim=node_in_dim,
        output_dim=MAX_RESULT_CLASS  # 18 výstupních tříd
    ).to(device)

    # Trénink
    all_files = load_files_from_results(CONFIG['results_dir'])
    train_files = all_files[:55]  # První 3 dokumenty pro trénink
    val_files = all_files[5:]  # Poslední 2 dokumenty pro validaci

    train_losses, val_losses, val_f1 = train_model(
        model,
        train_files,
        val_files,
        device,
        MAX_KEY_CLASS,
        MAX_VALUE_CLASS,
        MAX_RESULT_CLASS,
        epochs=CONFIG['epochs'],
        lr=CONFIG['learning_rate'],
        patience=CONFIG['patience'],
        output_dir="training_results"
    )

    # Uložení modelu
    torch.save(model.state_dict(), CONFIG['model_path'])
