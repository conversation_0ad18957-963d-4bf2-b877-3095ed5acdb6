import os
from typing import Dict, List, Tuple
from openpyxl import load_workbook
from DataClass import Class
from SBertClassifier import SBertClassifier

def load_training_data_with_scores_xlsx(data_path: str, model: 'SBertClassifier') -> Dict[str, Class]:
    """
    Načte tréninková data z XLSX a oskóruje příklady proti kotvě pomocí SBertClassifier.

    Args:
        data_path: cesta k XLSX souboru nebo adres<PERSON>ři obsahujícímu 'training_set.xlsx'
        model: instance SBertClassifier pro scoring

    Returns:
        slovník {category_name: Class(anchor, positive, negative)}
    """
    if os.path.isdir(data_path):
        data_path = os.path.join(data_path, 'training_set.xlsx')

    if not os.path.exists(data_path):
        raise FileNotFoundError(f"Soubor '{data_path}' nenalezen.")

    classes: Dict[str, Class] = {}
    wb = load_workbook(filename=data_path, read_only=True, data_only=True)
    sheet_names = wb.sheetnames

    print(f"Načítám {len(sheet_names)} kategorií ze souboru {data_path}...")

    for sheet_name in sheet_names:
        ws = wb[sheet_name]

        # Načteme kotvu z buňky A2 (řádek 2, sloupec 1)
        anchor_cell = ws.cell(row=2, column=1).value
        if not anchor_cell:
            print(f"  Upozornění: list '{sheet_name}' nemá kotvu v buňce A2, přeskočeno.")
            continue
        anchor_text = str(anchor_cell).strip()

        # Funkce pro scoring seznamu textů
        def score_list(column_idx: int) -> List[Tuple[str, float]]:
            result = []
            for row in ws.iter_rows(min_row=2, min_col=column_idx, max_col=column_idx):
                cell_val = row[0].value
                if cell_val:
                    text = str(cell_val).strip()
                    if text:
                        score = model.similarity(anchor_text, text)
                        result.append((text, score))
            return result

        positives = score_list(2)  # sloupec B
        negatives = score_list(3)  # sloupec C

        if not positives or not negatives:
            print(f"  Upozornění: list '{sheet_name}' nemá dostatek pozitivních/negativních příkladů, přeskočeno.")
            continue

        classes[sheet_name] = Class(
            anchor=anchor_text,
            positive=positives,
            negative=negatives
        )

        print(f"  Načteno '{sheet_name}': {len(positives)} pozitivních, {len(negatives)} negativních příkladů.")

    print(f"Načítání dat dokončeno. Celkem {len(classes)} kategorií.")
    return classes

from typing import Dict

def recompute_scores_inplace(classes: Dict[str, Class], classifier: SBertClassifier) -> None:
    """
    Přepočítá skóre pro již načtená data (dataclass Class) přímo in-place.
    Nemění texty, pouze aktualizuje hodnoty skóre pomocí dodaného classifieru.
    """
    for category_name, cls in classes.items():
        cls.positive = [
            (text, classifier.similarity(cls.anchor, text))
            for text, _ in cls.positive
        ]
        cls.negative = [
            (text, classifier.similarity(cls.anchor, text))
            for text, _ in cls.negative
        ]


import pandas as pd
from pandas import DataFrame

def load_training_data_to_df(data_path: str) -> DataFrame:
    """
    Načte tréninková data z XLSX bez skórování a vrátí je jako DataFrame.
    Načítá pouze kotvu (A2) a pozitivní příklady (sloupec B od řádku 2).

    Sloupce výstupu:
      - text: text kotvy nebo pozitivního příkladu
      - label: název kategorie (název listu)
      - is_anchor: True pro kotvu, False pro pozitivní příklady

    Args:
        data_path: cesta k XLSX souboru nebo adresáři obsahujícímu 'training_set.xlsx'

    Returns:
        pandas.DataFrame se sloupci [text, label, is_anchor]
    """
    # Umožní zadat adresář s defaultním názvem souboru
    if os.path.isdir(data_path):
        data_path = os.path.join(data_path, 'training_set.xlsx')

    if not os.path.exists(data_path):
        raise FileNotFoundError(f"Soubor '{data_path}' nenalezen.")

    wb = load_workbook(filename=data_path, read_only=True, data_only=True)
    sheet_names = wb.sheetnames

    rows = []
    print(f"Načítám (bez skórování) {len(sheet_names)} kategorií ze souboru {data_path}...")

    for sheet_name in sheet_names:
        ws = wb[sheet_name]

        # Kotva (A2)
        anchor_cell = ws.cell(row=2, column=1).value
        if not anchor_cell:
            print(f"  Upozornění: list '{sheet_name}' nemá kotvu v buňce A2, přeskočeno.")
            continue

        anchor_text = str(anchor_cell).strip()
        if not anchor_text:
            print(f"  Upozornění: list '{sheet_name}' má prázdnou kotvu, přeskočeno.")
            continue

        # Přidat kotvu
        rows.append({"text": anchor_text, "label": sheet_name, "is_anchor": True})

        # Pozitivní příklady (sloupec B)
        positives_count = 0
        for row in ws.iter_rows(min_row=2, min_col=2, max_col=2):
            cell_val = row[0].value
            if not cell_val:
                continue
            text = str(cell_val).strip()
            if not text:
                continue
            rows.append({"text": text, "label": sheet_name, "is_anchor": False})
            positives_count += 1

        if positives_count == 0:
            print(f"  Upozornění: list '{sheet_name}' nemá žádné pozitivní příklady.")
        else:
            print(f"  Načteno '{sheet_name}': 1 kotva, {positives_count} pozitivních příkladů.")

    df = pd.DataFrame(rows, columns=["text", "label", "is_anchor"])
    print(f"Načítání dat (bez skórování) dokončeno. Celkem {len(df)} řádků ze {len(sheet_names)} kategorií.")
    return df
