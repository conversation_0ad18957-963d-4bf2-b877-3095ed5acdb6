import pandas as pd
import numpy as np
import torch
from typing import List, <PERSON><PERSON>, Dict
from sentence_transformers import SentenceTransformer, losses, InputExample, util
from torch.utils.data import DataLoader
from torch.optim import AdamW
from sklearn.model_selection import train_test_split
from sentence_transformers.evaluation import EmbeddingSimilarityEvaluator
from SBertClassifier import SBertClassifier
from semantic_classifier.load_training_data_from_xlsx import load_training_data_to_df


class AnchorMNRLoss(torch.nn.Module):
    """
    Vlastní ztrátová funkce pro trénink na pevných kotvách.
    Používá Multi-Class Negative Log Likelihood Loss (MNRL).
    """

    def __init__(self, model: SentenceTransformer, anchors_dict: Dict[str, np.ndarray]):
        """
        Inicializace loss funkce.
        :param model: SBERT model
        :param anchors_dict: Slovník s pevn<PERSON>mi kotvami a jejich ID (textové labely)
        """
        super(<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, self).__init__()
        self.model = model
        self.anchors_dict = anchors_dict
        self.anchor_labels = list(anchors_dict.keys())
        self.anchor_embeddings = torch.tensor(
            np.array([anchors_dict[label] for label in self.anchor_labels]),
            dtype=torch.float32
        )
        self.loss_fct = torch.nn.CrossEntropyLoss()

    def forward(self, sentence_features: List[Dict[str, torch.Tensor]], labels: torch.Tensor) -> torch.Tensor:
        """
        Spuštění loss funkce na jednom batchi.
        :param sentence_features: Obsahuje embeddingy pro aktuální batch textů
        :param labels: Číselné ID (indexy) pro labely, odpovídající self.anchor_labels
        """
        # sentence_embedding: [batch_size, embedding_dim]
        sentence_embeddings = self.model(sentence_features[0])['sentence_embedding']

        # Normalizujeme embeddingy z batchu
        sentence_embeddings = util.normalize_embeddings(sentence_embeddings)

        # anchor_embeddings: [num_anchors, embedding_dim]
        # Přesuneme na stejné zařízení jako sentence_embeddings
        self.anchor_embeddings = self.anchor_embeddings.to(sentence_embeddings.device)

        # scores: [batch_size, num_anchors]
        # Vypočítáme kosinusovou podobnost mezi každým textem a každou kotvou
        scores = util.cos_sim(sentence_embeddings, self.anchor_embeddings)

        # loss_fct očekává:
        # scores: [batch_size, num_classes]
        # labels: [batch_size]
        return self.loss_fct(scores, labels)


def prepare_data_for_anchor_mining(df: pd.DataFrame, anchors_dict: Dict[str, np.ndarray]) -> Tuple[
    List[InputExample], List[InputExample]]:
    """
    Převede DataFrame na tréninkové a validační sady pro AnchorMNRLoss.
    """
    df_labeled = df[df['label'].notna()]
    train_df, val_df = train_test_split(df_labeled, test_size=0.2, random_state=42, stratify=df_labeled['label'])

    # Mapování labelu na index kotvy
    anchor_labels = list(anchors_dict.keys())
    label_to_id = {label: i for i, label in enumerate(anchor_labels)}

    # Tréninková data - každý text s ID své kotvy
    train_examples = []
    for _, row in train_df.iterrows():
        if row['label'] in label_to_id:
            train_examples.append(InputExample(texts=[row['text']], label=label_to_id[row['label']]))

    # Validační data - páry pro klasickou evaluaci
    val_examples = []
    val_df_labeled = val_df[val_df['label'].notna()]
    unique_labels_val = val_df_labeled['label'].unique()

    for label in unique_labels_val:
        texts = val_df_labeled[val_df_labeled['label'] == label]['text'].tolist()
        for i in range(len(texts)):
            for j in range(i + 1, len(texts)):
                val_examples.append(InputExample(texts=[texts[i], texts[j]], label=1.0))

        other_labels = [l for l in unique_labels_val if l != label]
        if other_labels:
            other_texts = val_df_labeled[val_df_labeled['label'] == other_labels[0]]['text'].tolist()
            if other_texts:
                val_examples.append(InputExample(texts=[texts[0], other_texts[0]], label=0.0))

    return train_examples, val_examples


def train_model_with_anchors(df: pd.DataFrame,
                             sbert_classifier: SBertClassifier,
                             epochs: int = 5,
                             learning_rate: float = 2e-5,
                             margin: float = 0.5):
    """
    Provede trénink modelu s využitím AnchorMNRLoss a pevnými kotvami.
    """
    # Krok 1: Vytvoříme slovník kotevních embeddingů
    # Použijeme model pro získání embeddingů z kotevních textů
    anchors_df = df[df['is_anchor'] == True]
    anchors_dict = {
        str(row['label']): sbert_classifier.model.encode(str(row['text']), convert_to_tensor=False)
        for _, row in anchors_df.iterrows()
    }

    # Krok 2: Příprava tréninkových a validačních dat
    train_examples, val_examples = prepare_data_for_anchor_mining(df, anchors_dict)

    if not train_examples:
        print("Upozornění: Pro trénink nebyly nalezeny žádné vhodné příklady.")
        return

    train_dataloader = DataLoader(train_examples, shuffle=True, batch_size=16)

    # Krok 3: Inicializace vlastní ztrátové funkce
    train_loss = AnchorMNRLoss(model=sbert_classifier.model, anchors_dict=anchors_dict)

    # Krok 4: Inicializace evaluátoru
    evaluator = EmbeddingSimilarityEvaluator.from_input_examples(val_examples, name='val')

    print("Zahajuji trénink s vlastní AnchorMNRLoss...")

    sbert_classifier.model.fit(
        train_objectives=[(train_dataloader, train_loss)],
        evaluator=evaluator,
        epochs=epochs,
        scheduler='WarmupLinear',
        optimizer_class=AdamW,
        optimizer_params={'lr': learning_rate},
        show_progress_bar=True,
        evaluation_steps=10,
        warmup_steps=10,
        weight_decay=0.01,
        output_path="fine_tuned_sbert",
        save_best_model=True,
        use_amp=False,
    )

    print("\nTrénink dokončen.")

    # Aktualizace prototypů
    sbert_classifier.create_and_save_prototypes(df.groupby('label')['text'].apply(list).to_dict())
    print("Prototypy aktualizovány a uloženy.")


if __name__ == "__main__":
    df = load_training_data_to_df("training_data/training_set.xlsx")
    sbert_model = SBertClassifier()
    train_model_with_anchors(df, sbert_model)
