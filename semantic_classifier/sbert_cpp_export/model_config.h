#ifndef MODEL_CONFIG_H
#define MODEL_CONFIG_H

// Auto-generated model configuration for SBertClassifier
namespace ModelConfig {
    constexpr int EMBEDDING_DIM = 768;
    constexpr int NUM_CLASSES = 25;
    constexpr int MAX_SEQUENCE_LENGTH = 512;  // Adjust based on your model

    // Class names (ordered as in prototypes matrix)
    const char* CLASS_NAMES[NUM_CLASSES] = {
        "Strán<PERSON>", "Faktura", "Dobro<PERSON>", "Zálohová faktura", "Dodací list", "Objednávka", "Č<PERSON>lo faktury", "<PERSON><PERSON><PERSON> dodacího listu", "<PERSON><PERSON><PERSON> objednáv<PERSON>", "Variabilní symbol", "Dodavatel", "Odběratel", "IČO", "DIČ", "Datum vystavení", "Datum splatnosti", "DUZP", "<PERSON><PERSON><PERSON> úč<PERSON>", "<PERSON><PERSON>d banky", "IBAN", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON> s DPH"
    };

    // Class IDs (ordered as in prototypes matrix)
    const int CLASS_IDS[NUM_CLASSES] = {
        0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24
    };

    // File paths
    constexpr const char* ONNX_MODEL_PATH = "text_encoder.onnx";
    constexpr const char* PROTOTYPES_PATH = "prototypes.bin";
    constexpr const char* PROTOTYPES_DIMS_PATH = "prototypes_dims.txt";
    constexpr const char* PROTOTYPES_SIMPLE_PATH = "prototypes_simple.bin";
    constexpr const char* TOKENIZER_PATH = "tokenizer";
}

// Suggested C++ structures
struct ClassificationResult {
    int class_id;
    const char* class_name;
    float confidence;
};

struct PrototypeMatrix {
    float* data;           // Raw float32 array [num_classes * embedding_dim]
    int num_classes;       // Number of classes
    int embedding_dim;     // Embedding dimension
};

// Usage example:
// 1. Load prototypes: PrototypeMatrix matrix = load_prototypes("prototypes.bin");
// 2. Compute text embedding: float* embedding = encode_text("some text");
// 3. Classify: ClassificationResult result = classify(embedding, matrix);

#endif // MODEL_CONFIG_H