#!/usr/bin/env python3
import os
import random
from pathlib import Path
from torch.utils.data import DataLoader
from sentence_transformers import InputExample, losses
from sentence_transformers.evaluation import TripletEvaluator
from load_training_data_from_xlsx import load_training_data_with_scores_xlsx, recompute_scores_inplace
from generate_triplets import generate_triplets
from SBertClassifier import SBertClassifier
from DataClass import Class
from sklearn.metrics.pairwise import cosine_similarity
import numpy as np


# Konfigurace
DATA_XLSX = Path("training_data/training_set.xlsx")
EPOCHS = 5  # Méně epoch pro adaptivní trénink
BATCH_SIZE = 16  # Menší batch size
POS_THRESHOLD = 0.85
NEG_THRESHOLD = 0.45
VALIDATION_SPLIT = 0.2
EVALUATION_STEPS = 5  # Častější evaluace
RANDOM_SEED = 42
LEARNING_RATE = 2e-5  # Vyšší learning rate než v původním

# Early stopping parametry
EARLY_STOPPING_PATIENCE = 2  # Kratší patience pro rychlejší adaptaci
EARLY_STOPPING_MIN_DELTA = 0.005  # Větší min_delta pro stabilitu

# Nastavení random seed
random.seed(RANDOM_SEED)


class EarlyStopping:
    """Early stopping implementace pro adaptivní trénink."""

    def __init__(self, patience=2, min_delta=0.005, restore_best_weights=True):
        self.patience = patience
        self.min_delta = min_delta
        self.restore_best_weights = restore_best_weights
        self.best_score = None
        self.counter = 0
        self.best_model_state = None

    def __call__(self, current_score, model):
        if self.best_score is None:
            self.best_score = current_score
            if self.restore_best_weights:
                self.best_model_state = model.state_dict().copy()
            return False

        if current_score > self.best_score + self.min_delta:
            self.best_score = current_score
            self.counter = 0
            if self.restore_best_weights:
                self.best_model_state = model.state_dict().copy()
            print(f"✅ Nové nejlepší skóre: {current_score:.4f}")
            return False
        else:
            self.counter += 1
            print(f"⏳ Žádné zlepšení ({self.counter}/{self.patience}), skóre: {current_score:.4f}")

            if self.counter >= self.patience:
                print(f"🛑 Early stopping! Nejlepší skóre: {self.best_score:.4f}")
                if self.restore_best_weights and self.best_model_state:
                    model.load_state_dict(self.best_model_state)
                    print("🔄 Obnoveny nejlepší váhy modelu")
                return True
            return False


def categorize_triplets_by_difficulty(triplets, classifier, pos_threshold=0.85, neg_threshold=0.45):
    """Kategorizuje triplety podle vzdálenosti od požadovaných prahů"""

    hard_triplets = []      # Nejvíce vzdálené od cíle - agresivní parametry
    medium_triplets = []    # Střední vzdálenost - střední parametry
    easy_triplets = []      # Blízko cíli - jemné parametry

    for anchor, positive, negative, strength in triplets:
        # Získání embeddingů
        anchor_emb = classifier.model.encode(anchor)
        pos_emb = classifier.model.encode(positive)
        neg_emb = classifier.model.encode(negative)

        # Výpočet podobností
        pos_sim = cosine_similarity([anchor_emb], [pos_emb])[0][0]
        neg_sim = cosine_similarity([anchor_emb], [neg_emb])[0][0]

        # Vzdálenost od požadovaných prahů
        pos_distance = abs(pos_sim - pos_threshold)  # Chceme pos_sim >= pos_threshold
        neg_distance = abs(neg_sim - neg_threshold)  # Chceme neg_sim <= neg_threshold

        # Celková vzdálenost od cíle
        total_distance = pos_distance + neg_distance

        triplet_data = (anchor, positive, negative, strength, pos_sim, neg_sim, total_distance)

        # Kategorizace podle vzdálenosti
        if total_distance > 0.8:  # Velmi vzdálené od cíle
            hard_triplets.append(triplet_data)
        elif total_distance > 0.4:  # Střední vzdálenost
            medium_triplets.append(triplet_data)
        else:  # Blízko cíli
            easy_triplets.append(triplet_data)

    return hard_triplets, medium_triplets, easy_triplets

def get_training_config_for_difficulty(difficulty_level):
    """Vrací konfiguraci tréninku podle obtížnosti tripletů"""

    if difficulty_level == "hard":
        return {
            'name': 'TĚŽKÉ triplety (nejvíce vzdálené od cíle)',
            'epochs': 10,           # Více epoch pro těžké případy
            'batch_size': 16,       # Menší batch pro stabilitu
            'learning_rate': 5e-5, # Vyšší LR pro agresivní učení
            'margin': 0.3,         # Větší margin
            'warmup_steps': 50,    # Více warmup kroků
            'scheduler': 'warmupLinear',
            'weight_decay': 0.02,  # Vyšší regularizace
            'emoji': '🔥'
        }
    elif difficulty_level == "medium":
        return {
            'name': 'STŘEDNÍ triplety (střední vzdálenost)',
            'epochs': 8,           # Střední počet epoch
            'batch_size': 16,      # Střední batch size
            'learning_rate': 2e-5, # Střední LR
            'margin': 0.2,         # Střední margin
            'warmup_steps': 25,    # Střední warmup
            'scheduler': 'warmupLinear',
            'weight_decay': 0.01,  # Střední regularizace
            'emoji': '⚖️'
        }
    else:  # easy
        return {
            'name': 'LEHKÉ triplety (blízko cíli)',
            'epochs': 5,           # Méně epoch, už jsou blízko
            'batch_size': 8,      # Větší batch pro efektivitu
            'learning_rate': 1e-5, # Nižší LR pro jemné doladění
            'margin': 0.1,         # Menší margin
            'warmup_steps': 10,    # Krátký warmup
            'scheduler': 'constantlr',
            'weight_decay': 0.005, # Nižší regularizace
            'emoji': '🎯'
        }

def train_triplet_category(classifier, triplets, val_triplets, difficulty_level, category_name):
    """Trénink jedné kategorie tripletů s příslušnými parametry"""

    if not triplets:
        print(f"⚠️  Žádné {difficulty_level} triplety k tréninku")
        return 0.0

    config = get_training_config_for_difficulty(difficulty_level)
    print(f"\n{config['emoji']} Trénuji {config['name']}")
    print(f"   Počet tripletů: {len(triplets)}")
    print(f"   Parametry: epochs={config['epochs']}, batch={config['batch_size']}, lr={config['learning_rate']:.0e}")

    # Vytvoření validačních dat
    val_anchors = [anchor for anchor, _, _, _ in val_triplets]
    val_positives = [positive for _, positive, _, _ in val_triplets]
    val_negatives = [negative for _, _, negative, _ in val_triplets]

    evaluator = TripletEvaluator(
        anchors=val_anchors,
        positives=val_positives,
        negatives=val_negatives,
        name=f"{category_name}_{difficulty_level}_evaluation"
    )

    # Převod na InputExample
    train_examples = [
        InputExample(texts=[anchor, positive, negative])
        for anchor, positive, negative, _, _, _, _ in triplets
    ]

    # DataLoader a loss
    dataloader = DataLoader(train_examples, shuffle=True, batch_size=config['batch_size'])
    train_loss = losses.TripletLoss(
        model=classifier.model,
        distance_metric=losses.TripletDistanceMetric.COSINE,
        triplet_margin=config['margin']
    )

    # Trénink
    best_score = 0.0
    for epoch in range(config['epochs']):
        print(f"  Epoch {epoch + 1}/{config['epochs']}")

        classifier.model.fit(
            train_objectives=[(dataloader, train_loss)],
            epochs=1,
            evaluator=None,
            evaluation_steps=0,
            warmup_steps=config['warmup_steps'] if epoch == 0 else 0,
            scheduler=config['scheduler'] if epoch == 0 else 'constantlr',
            show_progress_bar=False,
            use_amp=False,
            optimizer_params={
                'lr': config['learning_rate'],
                'eps': 1e-8,
                'weight_decay': config['weight_decay']
            }
        )

        # Evaluace
        eval_results = evaluator(classifier.model, output_path=classifier.model_dir)
        current_score = eval_results.get(f'{category_name}_{difficulty_level}_evaluation_cosine_accuracy', 0.0)
        print(f"    Accuracy: {current_score:.4f}")

        if current_score > best_score:
            best_score = current_score
            classifier.model.save(classifier.model_dir)

    print(f"  {config['emoji']} Nejlepší skóre pro {difficulty_level}: {best_score:.4f}")
    return best_score

def adaptive_train_on_category(category_name="adaptive"):
    """Adaptivní trénink podle původního konceptu - 3 sady parametrů podle složitosti"""

    print(f"🚀 ADAPTIVNÍ trénink podle složitosti tripletů")
    print("=" * 60)

    # Inicializace klasifikátoru
    classifier = SBertClassifier()

    # Načtení dat a výpočet skóre
    classes: dict[str, Class] = load_training_data_with_scores_xlsx(DATA_XLSX, classifier)

    # Generování tripletů
    triplets = generate_triplets(classes, pos_threshold=POS_THRESHOLD, neg_threshold=NEG_THRESHOLD)
    print(f"Celkem tripletů: {len(triplets)}")

    if not triplets:
        print("❌ Žádné triplety k tréninku!")
        return 0.0

    # Rozdělení na trénovací a validační data
    random.shuffle(triplets)
    split_idx = int(len(triplets) * (1 - VALIDATION_SPLIT))
    train_triplets = triplets[:split_idx]
    val_triplets = triplets[split_idx:]

    print(f"Trénovací: {len(train_triplets)}, Validační: {len(val_triplets)}")

    # Kategorizace podle složitosti
    print("\n🔍 Kategorizuji triplety podle vzdálenosti od cílových prahů...")
    hard_triplets, medium_triplets, easy_triplets = categorize_triplets_by_difficulty(
        train_triplets, classifier, POS_THRESHOLD, NEG_THRESHOLD
    )

    print(f"📊 Těžké: {len(hard_triplets)}, Střední: {len(medium_triplets)}, Lehké: {len(easy_triplets)}")

    # Postupný trénink podle složitosti
    scores = []

    # 1. Těžké triplety - agresivní parametry
    if hard_triplets:
        score = train_triplet_category(classifier, hard_triplets, val_triplets, "hard", category_name)
        scores.append(score)

    # 2. Střední triplety - střední parametry
    if medium_triplets:
        score = train_triplet_category(classifier, medium_triplets, val_triplets, "medium", category_name)
        scores.append(score)

    # 3. Lehké triplety - jemné parametry
    if easy_triplets:
        score = train_triplet_category(classifier, easy_triplets, val_triplets, "easy", category_name)
        scores.append(score)

    final_score = max(scores) if scores else 0.0
    return final_score





def main():
    """Hlavní funkce pro adaptivní trénink podle původního konceptu"""

    print("🎯 ADAPTIVNÍ TRÉNINK - 3 sady parametrů podle složitosti tripletů")
    print("=" * 70)
    print("🔥 Těžké triplety → Agresivní parametry (vysoký LR, velký margin)")
    print("⚖️  Střední triplety → Střední parametry")
    print("🎯 Lehké triplety → Jemné parametry (nízký LR, malý margin)")
    print("=" * 70)

    # Adaptivní trénink podle složitosti
    score = adaptive_train_on_category("adaptive_complexity")

    print("=" * 70)
    print(f"🏆 FINÁLNÍ SKÓRE adaptivního tréninku: {score:.4f}")
    print("✅ Adaptivní trénink podle složitosti dokončen!")


if __name__ == "__main__":
    main()
