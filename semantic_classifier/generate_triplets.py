from typing import Dict, List, Tuple, Union
from DataClass import Class

Triplet = Tuple[str, str, str, float]  # (anchor, positive_text, negative_text, strength)

def generate_triplets(data: Union[Dict[str, Class], List[Class]],
                      pos_threshold: float = 0.8,
                      neg_threshold: float = 0.65) -> List[Triplet]:
    # Umo<PERSON><PERSON> dict i list vstup
    items = data.items() if isinstance(data, dict) else [(None, cls) for cls in data]

    triplets: List[Triplet] = []

    for _, cls in items:
        # Kandi<PERSON><PERSON><PERSON> k <PERSON> (nejisté příklady)
        pos_candidates = [(txt, s) for txt, s in cls.positive if s <= pos_threshold]
        neg_candidates = [(txt, s) for txt, s in cls.negative if s >= neg_threshold]

        # Dopl<PERSON> „nejhoršími“ mimo hrani<PERSON>, ať máme vyvážený počet
        if len(pos_candidates) < len(neg_candidates):
            extra = len(neg_candidates) - len(pos_candidates)
            # pro pozitiva vezmi ty *nad* prahem, ale co nejnižší nad prahem
            worst_pos = sorted([(t, s) for t, s in cls.positive if s > pos_threshold],
                               key=lambda x: x[1])[:extra]
            pos_candidates.extend(worst_pos)
        elif len(neg_candidates) < len(pos_candidates):
            extra = len(pos_candidates) - len(neg_candidates)
            # pro negativa vezmi ta *pod* prahem, ale co nejblíž prahu (nejvyšší pod 0.65)
            worst_neg = sorted([(t, s) for t, s in cls.negative if s < neg_threshold],
                               key=lambda x: -x[1])[:extra]
            neg_candidates.extend(worst_neg)

        # Seřaď: pozitiva od nejhorších (nejnižší s), negativa od nejhorších (nejvyšší s)
        count = min(len(pos_candidates), len(neg_candidates))
        pos_candidates = sorted(pos_candidates, key=lambda x: x[1])[:count]
        neg_candidates = sorted(neg_candidates, key=lambda x: -x[1])[:count]

        # Vytvoř triplety a spočítej sílu = (0.8 - pos_s) + (neg_s - 0.65)
        for (p_txt, p_s), (n_txt, n_s) in zip(pos_candidates, neg_candidates):
            pos_delta = max(0.0, pos_threshold - p_s)
            neg_delta = max(0.0, n_s - neg_threshold)
            strength = pos_delta + neg_delta
            triplets.append((cls.anchor, p_txt, n_txt, strength))

    # (volitelné) seřadit triplet podle síly sestupně
    triplets.sort(key=lambda t: -t[3])
    return triplets