#!/usr/bin/env python3
"""
Test skript pro ověřen<PERSON> výpočtu TripletEvaluator accuracy.
<PERSON>uje, zda evaluátor správně počítá skóre.
"""

import random
from sentence_transformers.evaluation import TripletEvaluator
from SBertClassifier import SBertClassifier

def test_triplet_evaluator():
    """Test základní funkcionality TripletEvaluator."""
    print("🔍 Testování TripletEvaluator...")
    
    # Inicializace modelu
    classifier = SBertClassifier()
    
    # Vytvoření jednoduchých testovacích dat
    print("\n=== Test 1: Jednoduché pozitivní případy ===")
    
    # Velmi podobné texty (měly by mít vysokou accuracy)
    anchors = [
        "faktura číslo 123",
        "datum splatnosti",
        "celkem k úhradě"
    ]
    
    positives = [
        "faktura č. 123",  # velmi podobné
        "datum splatnosti faktury",  # podobné
        "celková částka k úhradě"  # podobné
    ]
    
    negatives = [
        "objednávka číslo 456",  # nepodobné
        "jméno dodavatele",  # nepodobné  
        "adresa odběratele"  # nepodobné
    ]
    
    evaluator = TripletEvaluator(
        anchors=anchors,
        positives=positives,
        negatives=negatives,
        name="test_simple"
    )
    
    result = evaluator(classifier.model)
    print(f"Výsledek jednoduchého testu: {result}")
    
    # Test 2: Obtížnější případy
    print("\n=== Test 2: Obtížnější případy ===")
    
    anchors2 = [
        "IČO: 12345678",
        "Variabilní symbol: 987654",
        "Číslo faktury: F-2024-001"
    ]
    
    positives2 = [
        "IČ: 12345678",  # trochu jiný formát
        "VS: 987654",  # zkratka
        "Faktura F-2024-001"  # jiné pořadí slov
    ]
    
    negatives2 = [
        "DIČ: CZ12345678",  # jiný typ čísla
        "Konstantní symbol: 123",  # jiný typ symbolu
        "Objednávka O-2024-001"  # jiný typ dokladu
    ]
    
    evaluator2 = TripletEvaluator(
        anchors=anchors2,
        positives=positives2,
        negatives=negatives2,
        name="test_difficult"
    )
    
    result2 = evaluator2(classifier.model)
    print(f"Výsledek obtížného testu: {result2}")
    
    # Test 3: Velmi obtížné případy (měly by mít nízkou accuracy)
    print("\n=== Test 3: Velmi obtížné případy ===")
    
    anchors3 = [
        "faktura",
        "datum",
        "částka"
    ]
    
    positives3 = [
        "dobropis",  # podobný typ dokumentu, ale jiný
        "čas",  # podobné, ale jiné
        "suma"  # podobné, ale jiné
    ]
    
    negatives3 = [
        "faktury",  # velmi podobné anchor
        "datum vystavení",  # velmi podobné anchor
        "celková částka"  # velmi podobné anchor
    ]
    
    evaluator3 = TripletEvaluator(
        anchors=anchors3,
        positives=positives3,
        negatives=negatives3,
        name="test_confusing"
    )
    
    result3 = evaluator3(classifier.model)
    print(f"Výsledek matoucího testu: {result3}")
    
    # Test 4: Prázdné nebo problematické případy
    print("\n=== Test 4: Problematické případy ===")
    
    try:
        # Test s prázdnými seznamy
        evaluator_empty = TripletEvaluator(
            anchors=[],
            positives=[],
            negatives=[],
            name="test_empty"
        )
        result_empty = evaluator_empty(classifier.model)
        print(f"Výsledek prázdného testu: {result_empty}")
    except Exception as e:
        print(f"Chyba při prázdném testu: {e}")
    
    try:
        # Test s jedním prvkem
        evaluator_single = TripletEvaluator(
            anchors=["test"],
            positives=["test pozitivní"],
            negatives=["test negativní"],
            name="test_single"
        )
        result_single = evaluator_single(classifier.model)
        print(f"Výsledek testu s jedním prvkem: {result_single}")
    except Exception as e:
        print(f"Chyba při testu s jedním prvkem: {e}")

def test_manual_similarity():
    """Manuální test podobnosti pro porovnání."""
    print("\n=== Manuální test podobnosti ===")
    
    classifier = SBertClassifier()
    
    # Test případy
    test_cases = [
        ("faktura číslo 123", "faktura č. 123", "objednávka číslo 456"),
        ("datum splatnosti", "datum splatnosti faktury", "jméno dodavatele"),
        ("IČO: 12345678", "IČ: 12345678", "DIČ: CZ12345678")
    ]
    
    for i, (anchor, positive, negative) in enumerate(test_cases, 1):
        pos_sim = classifier.similarity(anchor, positive)
        neg_sim = classifier.similarity(anchor, negative)
        
        print(f"\nTest {i}:")
        print(f"  Anchor: '{anchor}'")
        print(f"  Positive: '{positive}' (podobnost: {pos_sim:.4f})")
        print(f"  Negative: '{negative}' (podobnost: {neg_sim:.4f})")
        print(f"  Správně klasifikováno: {pos_sim > neg_sim}")

def analyze_evaluation_results(result):
    """Analyzuje výsledky evaluace."""
    print(f"\n=== Analýza výsledků ===")
    
    if isinstance(result, dict):
        print("Výsledek je slovník:")
        for key, value in result.items():
            print(f"  {key}: {value}")
            
        # Hledání hlavní metriky
        main_metrics = [
            'triplet_evaluation_cosine_accuracy',
            'cosine_accuracy', 
            'accuracy_cosine',
            'accuracy'
        ]
        
        for metric in main_metrics:
            if metric in result:
                print(f"Hlavní metrika '{metric}': {result[metric]}")
                break
    else:
        print(f"Výsledek je číslo: {result}")

def main():
    print("🧪 Test TripletEvaluator výpočtu accuracy")
    print("=" * 50)
    
    # Základní testy
    test_triplet_evaluator()
    
    # Manuální test pro porovnání
    test_manual_similarity()
    
    print("\n✅ Testy dokončeny")

if __name__ == "__main__":
    main()
