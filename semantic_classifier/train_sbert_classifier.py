import pandas as pd
import numpy as np
from typing import List, Tuple, Dict, Any

from sentence_transformers.util import pairwise_cos_sim
from torch.nn import CosineSimilarity

from SBertClassifier import SBertClassifier
from sentence_transformers import losses, InputExample, util
from torch.utils.data import DataLoader
from torch.optim import AdamW
from sklearn.model_selection import train_test_split
from sentence_transformers.evaluation import EmbeddingSimilarityEvaluator
from semantic_classifier.load_training_data_from_xlsx import load_training_data_to_df



def prepare_data_for_online_mining(df: pd.DataFrame) -> Tuple[List[InputExample], List[InputExample]]:
    """
    Převede DataFrame na tréninkové a validační sady.
    Pro online mining se tréninková data skládají z jednoho textu na příklad.
    """
    train_df, val_df = train_test_split(df, test_size=0.2, random_state=42, stratify=df['label'].fillna('unlabeled'))

    # Vytvoření mapování řetězců na číselné ID
    label_to_id = {label: i for i, label in enumerate(df['label'].dropna().unique())}

    # Zde je klíčová změna: Vytvoříme jednoduchý seznam všech textů s jejich štítky
    train_examples = []
    train_df_labeled = train_df[train_df['label'].notna()]
    for index, row in train_df_labeled.iterrows():
        train_examples.append(InputExample(texts=[row['text']], label=label_to_id[row['label']]))

    # Validační páry pro EmbeddingSimilarityEvaluator (zůstávají stejné)
    val_examples = []
    val_df_labeled = val_df[val_df['label'].notna()]
    unique_labels_val = val_df_labeled['label'].unique()

    for label in unique_labels_val:
        texts = val_df_labeled[val_df_labeled['label'] == label]['text'].tolist()
        for i in range(len(texts)):
            for j in range(i + 1, len(texts)):
                val_examples.append(InputExample(texts=[texts[i], texts[j]], label=1.0))

        other_labels = [l for l in unique_labels_val if l != label]
        if other_labels:
            other_texts = val_df_labeled[val_df_labeled['label'] == other_labels[0]]['text'].tolist()
            if other_texts:
                val_examples.append(InputExample(texts=[texts[0], other_texts[0]], label=0.0))

    return train_examples, val_examples

def prepare_data(df: pd.DataFrame) -> Tuple[List[InputExample], List[InputExample]]:
    """
    Převede DataFrame na tréninkové a validační sady objektů InputExample.
    """
    train_df, val_df = train_test_split(df, test_size=0.3, random_state=42, stratify=df['label'].fillna('unlabeled'))

    def create_examples_from_df(df_subset):
        examples = []
        anchors_by_label = df_subset[df_subset['is_anchor'] == True].groupby('label')['text'].apply(list).to_dict()

        for anchor_label, anchor_texts in anchors_by_label.items():
            positive_texts = df_subset[(df_subset['label'] == anchor_label) & (df_subset['is_anchor'] == False)][
                'text'].tolist()
            negative_texts = df_subset[df_subset['label'] != anchor_label]['text'].tolist()

            for anchor_text in anchor_texts:
                for positive_text in positive_texts:
                    for negative_text in negative_texts:
                        examples.append(InputExample(texts=[anchor_text, positive_text, negative_text]))
        return examples

    # Validační páry pro EmbeddingSimilarityEvaluator
    val_examples = []
    val_df_labeled = val_df[val_df['label'].notna()]
    unique_labels = val_df_labeled['label'].unique()

    for label in unique_labels:
        texts = val_df_labeled[val_df_labeled['label'] == label]['text'].tolist()
        for i in range(len(texts)):
            for j in range(i + 1, len(texts)):
                val_examples.append(InputExample(texts=[texts[i], texts[j]], label=1.0))  # Podobné páry

        # Přidáme i nepodobné páry
        other_labels = [l for l in unique_labels if l != label]
        if other_labels:
            other_texts = val_df_labeled[val_df_labeled['label'] == other_labels[0]]['text'].tolist()
            if other_texts:
                val_examples.append(InputExample(texts=[texts[0], other_texts[0]], label=0.0))  # Nepodobné páry

    # Vytvoření tréninkových příkladů
    train_examples = create_examples_from_df(train_df)

    return train_examples, val_examples


def train_model(df: pd.DataFrame,
                sbert_classifier: SBertClassifier,
                epochs: int = 1,
                learning_rate: float = 2e-5,
                margin: float = 0.2):
    """
    Provede trénink modelu s využitím early stoppingu.
    """
    train_examples, val_examples = prepare_data(df)

    if not train_examples:
        print("Upozornění: Pro trénink nebyly nalezeny žádné vhodné příklady.")
        return

    train_dataloader = DataLoader(train_examples, shuffle=True, batch_size=16)

    train_loss = losses.TripletLoss(
        model=sbert_classifier.model,
        distance_metric=losses.TripletDistanceMetric.COSINE,
        triplet_margin=margin
    )

    # Opravený evaluátor
    evaluator = EmbeddingSimilarityEvaluator.from_input_examples(val_examples, name='val')

    print("Zahajuji trénink s early stoppingem...")

    sbert_classifier.model.fit(
        train_objectives=[(train_dataloader, train_loss)],
        evaluator=evaluator,
        epochs=epochs,
        scheduler='WarmupLinear',
        optimizer_class=AdamW,
        optimizer_params={'lr': learning_rate},
        show_progress_bar=True,
        evaluation_steps=10,
        warmup_steps=10,
        output_path="fine_tuned_sbert",
        save_best_model=True
    )

    print("\nTrénink dokončen.")

    class_texts = df[df['label'].notna()].groupby('label')['text'].apply(list).to_dict()
    sbert_classifier.create_and_save_prototypes(class_texts)

    print("Prototypy aktualizovány a uloženy.")


def train_model_with_online_mining(df: pd.DataFrame, sbert_classifier: SBertClassifier, epochs: int = 50,
                                   learning_rate: float = 2e-5, margin: float = 0.5):
    """
       Provede trénink modelu s online hard negative miningem a early stoppingem.
       """
    train_examples, val_examples = prepare_data_for_online_mining(df)

    if not train_examples:
        print("Upozornění: Pro trénink nebyly nalezeny žádné vhodné příklady.")
        return

    # Datový loader nyní obsahuje jednotlivé texty s jejich štítky
    train_dataloader = DataLoader(train_examples, shuffle=True, batch_size=64)

    # Správné použití BatchAllTripletLoss - nepotřebuje distance_metric
    train_loss = losses.BatchAllTripletLoss(
        model=sbert_classifier.model,
        margin=margin
    )

    evaluator = EmbeddingSimilarityEvaluator.from_input_examples(val_examples, name='val')

    print("Zahajuji trénink s BatchAllTripletLoss...")

    sbert_classifier.model.fit(
        train_objectives=[(train_dataloader, train_loss)],
        evaluator=evaluator,
        epochs=epochs,
        scheduler='WarmupLinear',
        optimizer_class=AdamW,
        optimizer_params={'lr': learning_rate},
        show_progress_bar=True,
        evaluation_steps=10,
        warmup_steps=100,
        output_path="fine_tuned_sbert",
        save_best_model=True
    )

    print("\nTrénink dokončen.")

    class_texts = df[df['label'].notna()].groupby('label')['text'].apply(list).to_dict()
    sbert_classifier.create_and_save_prototypes(class_texts)

    print("Prototypy aktualizovány a uloženy.")

if __name__ == "__main__":

    df = load_training_data_to_df("training_data/training_set.xlsx")

    # Inicializace vašeho klasifikátoru
    sbert_model = SBertClassifier()

    # Spuštění tréninku
    train_model(df, sbert_model)

print("Model je nyní připraven pro klasifikaci s vylepšenými prototypy.")
