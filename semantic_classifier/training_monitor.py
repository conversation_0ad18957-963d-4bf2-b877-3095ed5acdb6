#!/usr/bin/env python3
"""
Monitoring skript pro sledování pokroku tréninku v reálném čase.
Čte logy z tréninku a zobrazuje grafy pokroku.
"""

import os
import time
import re
from pathlib import Path
import matplotlib.pyplot as plt
from collections import defaultdict
import numpy as np

class TrainingMonitor:
    def __init__(self, log_dir="fine_tuned_sbert"):
        self.log_dir = Path(log_dir)
        self.metrics = defaultdict(list)
        self.steps = []
        
    def parse_log_file(self, log_file):
        """Parsuje log soubor a extrahuje metriky."""
        if not os.path.exists(log_file):
            return
            
        with open(log_file, 'r') as f:
            content = f.read()
            
        # Regex patterns pro různé metriky
        patterns = {
            'triplet_accuracy': r'triplet_evaluation_accuracy:\s*([\d.]+)',
            'loss': r'loss:\s*([\d.]+)',
            'step': r'step:\s*(\d+)',
            'epoch': r'epoch:\s*(\d+)'
        }
        
        for metric, pattern in patterns.items():
            matches = re.findall(pattern, content)
            if matches:
                self.metrics[metric] = [float(m) for m in matches]
    
    def monitor_training(self, update_interval=30):
        """Sleduje trénink v reálném čase."""
        print("🔍 Spouštím monitoring tréninku...")
        print(f"📁 Sledovaná složka: {self.log_dir}")
        print(f"🔄 Aktualizace každých {update_interval} sekund")
        print("Stiskněte Ctrl+C pro ukončení")
        
        plt.ion()  # Interaktivní režim
        fig, axes = plt.subplots(2, 2, figsize=(15, 10))
        fig.suptitle('Monitoring tréninku v reálném čase', fontsize=16)
        
        try:
            while True:
                # Hledání log souborů
                log_files = list(self.log_dir.glob("*.log"))
                if not log_files:
                    # Zkus najít evaluation výsledky
                    eval_files = list(self.log_dir.glob("**/triplet_evaluation_results.csv"))
                    if eval_files:
                        self.parse_evaluation_results(eval_files[0])
                
                for log_file in log_files:
                    self.parse_log_file(log_file)
                
                # Aktualizace grafů
                self.update_plots(axes)
                plt.pause(0.1)
                
                time.sleep(update_interval)
                
        except KeyboardInterrupt:
            print("\n⏹️  Monitoring ukončen")
            plt.ioff()
            plt.show()
    
    def parse_evaluation_results(self, eval_file):
        """Parsuje CSV soubor s výsledky evaluace."""
        try:
            import pandas as pd
            df = pd.read_csv(eval_file)
            
            if 'step' in df.columns and 'triplet_accuracy' in df.columns:
                self.metrics['step'] = df['step'].tolist()
                self.metrics['triplet_accuracy'] = df['triplet_accuracy'].tolist()
                
        except ImportError:
            print("⚠️  Pandas není dostupný pro čtení CSV")
        except Exception as e:
            print(f"⚠️  Chyba při čtení evaluačních výsledků: {e}")
    
    def update_plots(self, axes):
        """Aktualizuje grafy s nejnovějšími daty."""
        # Vyčisti grafy
        for ax in axes.flat:
            ax.clear()
        
        # Graf 1: Triplet Accuracy
        if 'triplet_accuracy' in self.metrics and self.metrics['triplet_accuracy']:
            steps = list(range(len(self.metrics['triplet_accuracy'])))
            axes[0,0].plot(steps, self.metrics['triplet_accuracy'], 'b-o', markersize=4)
            axes[0,0].set_title('Triplet Accuracy')
            axes[0,0].set_xlabel('Evaluation Step')
            axes[0,0].set_ylabel('Accuracy')
            axes[0,0].grid(True, alpha=0.3)
            
            # Zobraz nejnovější hodnotu
            if self.metrics['triplet_accuracy']:
                latest = self.metrics['triplet_accuracy'][-1]
                axes[0,0].text(0.02, 0.98, f'Nejnovější: {latest:.4f}', 
                              transform=axes[0,0].transAxes, verticalalignment='top',
                              bbox=dict(boxstyle='round', facecolor='wheat', alpha=0.8))
        
        # Graf 2: Loss (pokud je dostupný)
        if 'loss' in self.metrics and self.metrics['loss']:
            steps = list(range(len(self.metrics['loss'])))
            axes[0,1].plot(steps, self.metrics['loss'], 'r-o', markersize=4)
            axes[0,1].set_title('Training Loss')
            axes[0,1].set_xlabel('Step')
            axes[0,1].set_ylabel('Loss')
            axes[0,1].grid(True, alpha=0.3)
        
        # Graf 3: Pokrok v čase
        current_time = time.time()
        if hasattr(self, 'start_time'):
            elapsed = (current_time - self.start_time) / 60  # minuty
            if 'triplet_accuracy' in self.metrics:
                time_points = np.linspace(0, elapsed, len(self.metrics['triplet_accuracy']))
                axes[1,0].plot(time_points, self.metrics['triplet_accuracy'], 'g-o', markersize=4)
                axes[1,0].set_title('Pokrok v čase')
                axes[1,0].set_xlabel('Čas (minuty)')
                axes[1,0].set_ylabel('Triplet Accuracy')
                axes[1,0].grid(True, alpha=0.3)
        else:
            self.start_time = current_time
        
        # Graf 4: Statistiky
        axes[1,1].text(0.1, 0.9, 'Statistiky tréninku:', fontsize=14, weight='bold',
                      transform=axes[1,1].transAxes)
        
        stats_text = []
        if 'triplet_accuracy' in self.metrics and self.metrics['triplet_accuracy']:
            acc_data = self.metrics['triplet_accuracy']
            stats_text.extend([
                f"Evaluací: {len(acc_data)}",
                f"Nejlepší accuracy: {max(acc_data):.4f}",
                f"Současná accuracy: {acc_data[-1]:.4f}",
                f"Zlepšení: {acc_data[-1] - acc_data[0]:.4f}" if len(acc_data) > 1 else "Zlepšení: N/A"
            ])
        
        if 'loss' in self.metrics and self.metrics['loss']:
            loss_data = self.metrics['loss']
            stats_text.extend([
                f"",
                f"Nejnižší loss: {min(loss_data):.4f}",
                f"Současný loss: {loss_data[-1]:.4f}"
            ])
        
        for i, text in enumerate(stats_text):
            axes[1,1].text(0.1, 0.8 - i*0.08, text, fontsize=12,
                          transform=axes[1,1].transAxes)
        
        axes[1,1].set_xlim(0, 1)
        axes[1,1].set_ylim(0, 1)
        axes[1,1].axis('off')
        
        plt.tight_layout()
    
    def generate_report(self):
        """Vygeneruje finální report po dokončení tréninku."""
        print("\n📊 Generuji finální report...")
        
        if not self.metrics:
            print("⚠️  Žádná data pro report")
            return
        
        # Vytvoř finální grafy
        fig, axes = plt.subplots(2, 2, figsize=(15, 10))
        fig.suptitle('Finální report tréninku', fontsize=16)
        
        self.update_plots(axes)
        
        # Ulož report
        report_path = self.log_dir / "training_report.png"
        plt.savefig(report_path, dpi=300, bbox_inches='tight')
        print(f"📁 Report uložen: {report_path}")
        
        # Textový report
        report_text_path = self.log_dir / "training_summary.txt"
        with open(report_text_path, 'w') as f:
            f.write("=== FINÁLNÍ REPORT TRÉNINKU ===\n\n")
            
            if 'triplet_accuracy' in self.metrics:
                acc_data = self.metrics['triplet_accuracy']
                f.write(f"Triplet Accuracy:\n")
                f.write(f"  Počet evaluací: {len(acc_data)}\n")
                f.write(f"  Počáteční: {acc_data[0]:.4f}\n")
                f.write(f"  Finální: {acc_data[-1]:.4f}\n")
                f.write(f"  Nejlepší: {max(acc_data):.4f}\n")
                f.write(f"  Zlepšení: {acc_data[-1] - acc_data[0]:.4f}\n\n")
            
            if 'loss' in self.metrics:
                loss_data = self.metrics['loss']
                f.write(f"Training Loss:\n")
                f.write(f"  Počáteční: {loss_data[0]:.4f}\n")
                f.write(f"  Finální: {loss_data[-1]:.4f}\n")
                f.write(f"  Nejnižší: {min(loss_data):.4f}\n")
        
        print(f"📁 Textový report: {report_text_path}")

def main():
    import argparse
    parser = argparse.ArgumentParser(description='Monitor tréninku semantic classifieru')
    parser.add_argument('--log-dir', default='fine_tuned_sbert', 
                       help='Složka s logy tréninku')
    parser.add_argument('--interval', type=int, default=30,
                       help='Interval aktualizace v sekundách')
    parser.add_argument('--report-only', action='store_true',
                       help='Pouze vygeneruj report z existujících dat')
    
    args = parser.parse_args()
    
    monitor = TrainingMonitor(args.log_dir)
    
    if args.report_only:
        monitor.generate_report()
    else:
        monitor.monitor_training(args.interval)

if __name__ == "__main__":
    main()
