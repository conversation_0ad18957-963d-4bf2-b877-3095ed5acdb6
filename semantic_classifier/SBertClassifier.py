"""
Implemetace je finální. Změny jsou možné pouze po předchozím dotazu.
"""

import os
import json
import struct
import numpy as np
import torch
from typing import List, Optional, Dict, Tuple
from sentence_transformers import SentenceTransformer, models

class SBertClassifier:
    """
    Minimalistická třída pro sémantickou klasifikaci kompatibilní s C++.

    Po inicializaci se automaticky načte model a prototypy (pokud existují).
    Implementuje základní metody pro embeddingy, podobnost a správu prototypů.
    """

    def __init__(self, 
                 model_name: str = "sentence-transformers/paraphrase-multilingual-mpnet-base-v2",
                 model_dir: str = "fine_tuned_sbert", 
                 device: str = "cpu"):
        self.model_name = model_name
        self.model_dir = model_dir
        self.device = device

        # Inicializace atributů
        self.model: SentenceTransformer = None
        self.embedding_dim: int = 0
        self.class_names: List[str] = []
        self.class_ids: List[int] = []
        self.proto_matrix: Optional[np.ndarray] = None

        # Načti model a prototypy
        self._load_model()
        self._load_prototypes()

        print(f"SBertClassifier inicializován (device: {self.device}, prototypy: {len(self.class_names)})")

    def _is_valid_model_dir(self, path: str) -> bool:
        required_files = {"config.json", "pytorch_model.bin", "model.safetensors"}
        return os.path.isdir(path) and required_files.intersection(os.listdir(path))

    def _load_model(self):
        """Načte model z lokální složky nebo stáhne z HuggingFace."""
        if self._is_valid_model_dir(self.model_dir):
            print(f"Načítám lokální model ze složky: {self.model_dir}")
            self.model = SentenceTransformer(self.model_dir, device=self.device)
        else:
            print(f"Stahuji model z HuggingFace: {self.model_name}")
            word_embedding_model = models.Transformer(self.model_name)
            pooling_model = models.Pooling(word_embedding_model.get_word_embedding_dimension())

            self.model = SentenceTransformer(
                modules=[word_embedding_model, pooling_model],
                device=self.device
            )
            os.makedirs(self.model_dir, exist_ok=True)
            self.model.save(self.model_dir)
            print(f"Model uložen do: {self.model_dir}")

        # Rozměr embeddingů
        self.embedding_dim = self.model.get_sentence_embedding_dimension()

    def _load_prototypes(self) -> bool:
        """Načte prototypy z binárního formátu"""
        filepath = os.path.join(self.model_dir, "prototypes.bin")

        if not os.path.exists(filepath):
            print(f"Soubor {filepath} neexistuje")
            self._reset_prototypes()
            return False

        try:
            with open(filepath, 'rb') as f:
                # Načtení header
                num_classes, = struct.unpack('I', f.read(4))
                embedding_dim, = struct.unpack('I', f.read(4))

                # Načtení prototypů
                matrix_bytes = f.read(num_classes * embedding_dim * 4)  # float32 = 4 bytes
                proto_matrix = np.frombuffer(matrix_bytes, dtype=np.float32)
                proto_matrix = proto_matrix.reshape(num_classes, embedding_dim)

                # Načtení názvů tříd
                class_names = []
                for _ in range(num_classes):
                    name_len, = struct.unpack('I', f.read(4))
                    name_bytes = f.read(name_len)
                    class_name = name_bytes.decode('utf-8')
                    class_names.append(class_name)

                # Uložení do instance
                self.proto_matrix = proto_matrix
                self.class_names = class_names
                self.embedding_dim = embedding_dim

                print(f"Prototypy načteny: {num_classes} tříd, dimenze: {embedding_dim}")
                return True

        except Exception as e:
            print(f"Chyba při načítání prototypů: {e}")
            self._reset_prototypes()
            return False

    def _reset_prototypes(self):
        self.proto_matrix = None
        self.class_names = []

    def encode(self, texts) -> np.ndarray:
        if self.model is None:
            raise RuntimeError("Model není načten.")
        if isinstance(texts, str):
            texts = [texts]
        embeddings = self.model.encode(texts, convert_to_tensor=False)
        return np.array(embeddings, dtype=np.float32)

    def similarity(self, text1: str, text2: str) -> float:
        embeddings = self.encode([text1, text2])
        # Normalizace embeddingů
        embeddings /= np.linalg.norm(embeddings, axis=1, keepdims=True)
        cosine_sim = float(np.dot(embeddings[0], embeddings[1]))
        normalized = (cosine_sim + 1.0) / 2.0
        return max(0.0, min(1.0, normalized))

    def create_and_save_prototypes(self, class_texts: Dict[str, List[str]]):
        """Vytvoří prototypy z trénovacích textů a uloží je v binárním formátu pro C++"""
        if not class_texts:
            raise ValueError("class_texts nesmí být prázdné")

        print(f"Vytvářím prototypy pro {len(class_texts)} tříd...")

        prototypes = []
        class_names = []

        for class_name, texts in class_texts.items():
            if not texts:
                raise ValueError(f"Třída '{class_name}' nemá žádné texty")

            # Vytvoření embeddingů a centroidu
            embeddings = self.encode(texts)
            centroid = np.mean(embeddings, axis=0).astype(np.float32)
            prototypes.append(centroid)
            class_names.append(class_name)

        # Normalizace prototypů
        prototypes = np.array(prototypes, dtype=np.float32)
        prototypes /= np.linalg.norm(prototypes, axis=1, keepdims=True)

        # Uložení do instance
        self.proto_matrix = np.array(prototypes, dtype=np.float32)
        self.class_names = class_names
        self.embedding_dim = self.proto_matrix.shape[1]

        # Uložení v binárním formátu
        self._save_binary_format()
        print(f"Prototypy vytvořeny a uloženy: {len(self.class_names)} tříd, "
              f"dimenze: {self.embedding_dim}")

    def _save_binary_format(self):
        """Uloží prototypy v binárním formátu optimalizovaném pro C++"""
        if self.proto_matrix is None:
            return

        os.makedirs(self.model_dir, exist_ok=True)

        filepath = os.path.join(self.model_dir, "prototypes.bin")

        with open(filepath, 'wb') as f:
            # Header: počet tříd a dimenze embeddingu
            num_classes = len(self.class_names)
            f.write(struct.pack('I', num_classes))  # uint32_t
            f.write(struct.pack('I', self.embedding_dim))  # uint32_t

            # Prototypy jako raw float32 data (row-major order)
            f.write(self.proto_matrix.tobytes())

            # Názvy tříd s jejich délkami
            for name in self.class_names:
                name_bytes = name.encode('utf-8')
                f.write(struct.pack('I', len(name_bytes)))  # délka string
                f.write(name_bytes)  # string data

        print(f"Binární model uložen: {filepath}")

    def classify(self, text: str) -> Tuple[int, str, float]:
        """Vrací (class_id, class_name, confidence_score) pro jediný text."""
        if self.proto_matrix is None:
            raise RuntimeError("Prototypy nejsou načteny.")

        text_embedding = self.encode([text])[0]  # (D,)
        # Normalizace textového embeddingu
        text_embedding /= np.linalg.norm(text_embedding)
        similarities = np.dot(text_embedding, self.proto_matrix.T)  # (C,)
        best_idx = int(np.argmax(similarities))
        best_score = float(similarities[best_idx])
        normalized_score = max(0.0, min(1.0, (best_score + 1.0) / 2.0))

        return best_idx, self.class_names[best_idx], normalized_score

    def classify_batch(self, texts: List[str]) -> List[Tuple[int, str, float]]:
        """Vrací (class_id, class_name, confidence_score)"""
        if self.proto_matrix is None:
            raise RuntimeError("Prototypy nejsou načteny.")

        if not texts:
            return []

        text_embeddings = self.encode(texts)
        text_embeddings /= np.linalg.norm(text_embeddings, axis=1, keepdims=True)
        similarities = np.dot(text_embeddings, self.proto_matrix.T)
        best_indices = np.argmax(similarities, axis=1)
        best_scores = similarities[np.arange(len(texts)), best_indices]
        normalized_scores = np.clip((best_scores + 1.0) / 2.0, 0.0, 1.0)

        return [
            (int(idx+2), self.class_names[idx], float(score))
            for idx, score in zip(best_indices, normalized_scores)
        ]

    def get_prototype_matrix(self) -> np.ndarray:
        """
        Vrátí matici prototypů pro export do C++.

        Returns:
            numpy array shape: (n_classes, embedding_dim)
        """
        if self.proto_matrix is None:
            raise RuntimeError("Prototypy nejsou načteny.")
        return self.proto_matrix.copy()

    def export_for_cpp(self, base_path: str, export_model: bool = True):
        """
        Kompletní export pro C++ - model, prototypy, tokenizer a metadata.

        Args:
            base_path: cesta k výstupní složce
            export_model: zda exportovat také ONNX model (může být pomalé)
        """
        if self.proto_matrix is None:
            raise RuntimeError("Prototypy nejsou načteny.")

        os.makedirs(base_path, exist_ok=True)
        print(f"Exportuji do: {base_path}")

        # 1. Export prototypů jako raw binary (nejrychlejší pro C++)
        self._export_prototypes_raw(os.path.join(base_path, "prototypes.bin"))

        # 2. Export ONNX modelu (volitelné - může být pomalé)
        if export_model:
            try:
                self._export_text_encoder_onnx(base_path)
                print("✅ ONNX model exportován")
            except Exception as e:
                print(f"❌ ONNX export selhal: {e}")
                print("💡 Zkuste: pip install torch onnx optimum")

        # 3. Export tokenizer files
        self._export_tokenizer_files(base_path)

        # 4. C++ header s metadata
        self._generate_cpp_header(os.path.join(base_path, "model_config.h"))

        # 5. Jednoduchý binární export prototypů (kompatibilní s původní metodou)
        self._export_prototypes_simple(os.path.join(base_path, "prototypes_simple.bin"))

        print(f"\n📁 Export dokončen do: {base_path}")
        print("📝 Pro C++: použijte prototypes.bin + model_config.h")
        if export_model:
            print("📝 Pro plnou funkcionalnost: text_encoder.onnx + tokenizer/")

    def _export_prototypes_raw(self, filepath: str):
        """
        Export prototypů jako raw float32 array pro rychlé načtení v C++.
        Format: [float32 matrix: num_classes × embedding_dim]
        """
        if self.proto_matrix is None:
            raise RuntimeError("Žádné prototypy k exportu")

        # Uložit jako raw binary float32
        self.proto_matrix.astype(np.float32).tofile(filepath)

        # Uložit rozměry vedle
        dims_path = filepath.replace('.bin', '_dims.txt')
        with open(dims_path, 'w') as f:
            f.write(f"{len(self.class_names)} {self.embedding_dim}")

        print(f"Raw prototypy: {len(self.class_names)} tříd × {self.embedding_dim} dimenzí")
        print(f"  Soubor: {filepath}")
        print(f"  Rozměry: {dims_path}")

    def _export_prototypes_simple(self, filepath: str):
        """
        Jednoduchý binární export (kompatibilní s původní metodou).
        Format: [num_classes][embedding_dim][class1_name_len][class1_name][class1_embedding]...
        """
        with open(filepath, 'wb') as f:
            # Header: počet tříd, rozměr embeddingů
            f.write(len(self.class_names).to_bytes(4, 'little'))
            f.write(self.embedding_dim.to_bytes(4, 'little'))

            # Pro každou třídu: délka názvu, název, embedding
            for i, class_name in enumerate(self.class_names):
                name_bytes = class_name.encode('utf-8')
                f.write(len(name_bytes).to_bytes(4, 'little'))
                f.write(name_bytes)
                f.write(self.proto_matrix[i].tobytes())

        print(f"Jednoduché prototypy: {filepath}")

    def _export_text_encoder_onnx(self, base_path: str):
        """
        Exportuje pouze text encoding část sentence transformeru do ONNX.
        Optimalizováno pro text → embedding inference v C++.
        """
        if self.model is None:
            raise RuntimeError("Model není načten")

        try:
            # Získej underlying transformer model
            transformer_model = self.model[0].auto_model  # První modul
            tokenizer = self.model.tokenizer

            # Dummy input pro ONNX tracing
            sample_text = "Hello world"
            inputs = tokenizer(sample_text, return_tensors="pt", padding=True, truncation=True)

            # Nastavit model do eval módu
            transformer_model.eval()

            # ONNX export
            onnx_path = os.path.join(base_path, "text_encoder.onnx")

            with torch.no_grad():
                torch.onnx.export(
                    transformer_model,
                    (inputs['input_ids'], inputs['attention_mask']),
                    onnx_path,
                    input_names=['input_ids', 'attention_mask'],
                    output_names=['embeddings'],
                    dynamic_axes={
                        'input_ids': {0: 'batch_size', 1: 'sequence_length'},
                        'attention_mask': {0: 'batch_size', 1: 'sequence_length'},
                        'embeddings': {0: 'batch_size'}
                    },
                    opset_version=14,  # Novější verze pro lepší kompatibilitu
                    do_constant_folding=True
                )
            print(f"ONNX model: {onnx_path}")

        except ImportError as e:
            raise RuntimeError(f"ONNX export vyžaduje torch: {e}")
        except Exception as e:
            raise RuntimeError(f"ONNX export selhal: {e}")

    def _export_tokenizer_files(self, base_path: str):
        """
        Exportuje tokenizer files pro C++ (vocab, merges, config).
        """
        if self.model is None:
            return

        try:
            tokenizer = self.model.tokenizer
            tokenizer_path = os.path.join(base_path, "tokenizer")

            # Uložit tokenizer files
            tokenizer.save_pretrained(tokenizer_path)

            # Dodatečné info pro C++
            tokenizer_info = {
                "model_max_length": tokenizer.model_max_length,
                "vocab_size": len(tokenizer),
                "pad_token_id": tokenizer.pad_token_id,
                "cls_token_id": getattr(tokenizer, 'cls_token_id', None),
                "sep_token_id": getattr(tokenizer, 'sep_token_id', None),
            }

            with open(os.path.join(base_path, "tokenizer_config.json"), 'w') as f:
                json.dump(tokenizer_info, f, indent=2)

            print(f"Tokenizer: {tokenizer_path}")

        except Exception as e:
            print(f"⚠️  Tokenizer export selhal: {e}")

    def _generate_cpp_header(self, header_path: str):
        """
        Generuje C++ header s konstantami a strukturami.
        """
        header_content = f"""#ifndef MODEL_CONFIG_H
#define MODEL_CONFIG_H

// Auto-generated model configuration for SBertClassifier
namespace ModelConfig {{
    constexpr int EMBEDDING_DIM = {self.embedding_dim};
    constexpr int NUM_CLASSES = {len(self.class_names)};
    constexpr int MAX_SEQUENCE_LENGTH = 512;  // Adjust based on your model

    // Class names (ordered as in prototypes matrix)
    const char* CLASS_NAMES[NUM_CLASSES] = {{
        {', '.join([f'"{name}"' for name in self.class_names])}
    }};

    // Class IDs (ordered as in prototypes matrix)
    const int CLASS_IDS[NUM_CLASSES] = {{
        {', '.join([str(i) for i in range(len(self.class_names))])}
    }};

    // File paths
    constexpr const char* ONNX_MODEL_PATH = "text_encoder.onnx";
    constexpr const char* PROTOTYPES_PATH = "prototypes.bin";
    constexpr const char* PROTOTYPES_DIMS_PATH = "prototypes_dims.txt";
    constexpr const char* PROTOTYPES_SIMPLE_PATH = "prototypes_simple.bin";
    constexpr const char* TOKENIZER_PATH = "tokenizer";
}}

// Suggested C++ structures
struct ClassificationResult {{
    int class_id;
    const char* class_name;
    float confidence;
}};

struct PrototypeMatrix {{
    float* data;           // Raw float32 array [num_classes * embedding_dim]
    int num_classes;       // Number of classes
    int embedding_dim;     // Embedding dimension
}};

// Usage example:
// 1. Load prototypes: PrototypeMatrix matrix = load_prototypes("prototypes.bin");
// 2. Compute text embedding: float* embedding = encode_text("some text");
// 3. Classify: ClassificationResult result = classify(embedding, matrix);

#endif // MODEL_CONFIG_H"""

        with open(header_path, 'w') as f:
            f.write(header_content)

        print(f"C++ header: {header_path}")

    def info(self):
        """Vypíše informace o klasifikátoru."""
        print(f"=== SBertClassifier Info ===")
        print(f"Model: {self.model_name}")
        print(f"Model dir: {self.model_dir}")
        print(f"Device: {self.device}")
        print(f"Embedding dim: {self.embedding_dim}")
        print(f"Počet tříd: {len(self.class_names)}")
        if self.class_names:
            print(f"Třídy: {', '.join(self.class_names)}")
        print(f"Prototypy načteny: {'Ano' if self.proto_matrix is not None else 'Ne'}")
        print("=" * 30)
