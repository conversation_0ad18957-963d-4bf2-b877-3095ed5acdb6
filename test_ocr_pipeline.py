#!/usr/bin/env python3
"""
Jednoduchý test pro OcrPipeline - zpracuje první soubor ze složky Data.

Tento test:
1. Najde první PDF soubor ve složce Data
2. Zpracuje ho pomocí OcrPipeline
3. Zobrazí základní statistiky o výsledcích
"""

import os
import glob
import pandas as pd
from pdf2image import convert_from_path
import numpy as np

from OCR.OcrPipeline import OcrPipeline


def get_first_pdf_from_data():
    """
    Najde první PDF soubor ve složce Data.
    
    Returns:
        str: Cesta k prvnímu PDF souboru nebo None pokud žádný nenajde
    """
    data_folder = "Data"
    
    if not os.path.exists(data_folder):
        print(f"❌ Složka {data_folder} neexistuje!")
        return None
    
    # Najdi všechny PDF soubory
    pdf_files = glob.glob(os.path.join(data_folder, "*.pdf"))
    
    if not pdf_files:
        print(f"❌ Ve složce {data_folder} nejsou žádné PDF soubory!")
        return None
    
    # Seřaď podle názvu a vezmi první
    pdf_files.sort()
    first_pdf = pdf_files[0]
    
    print(f"📄 Nalezen první PDF soubor: {first_pdf}")
    return first_pdf


def convert_pdf_to_image(pdf_path):
    """
    Převede první stránku PDF na obrázek.
    
    Args:
        pdf_path (str): Cesta k PDF souboru
        
    Returns:
        np.ndarray: Obrázek jako numpy array nebo None při chybě
    """
    try:
        print(f"🔄 Převádím PDF na obrázek: {pdf_path}")
        
        # Převeď první stránku PDF na obrázek
        images = convert_from_path(pdf_path, dpi=300, first_page=1, last_page=1)
        
        if not images:
            print("❌ Nepodařilo se převést PDF na obrázek!")
            return None
        
        # Převeď PIL obrázek na numpy array
        image = np.array(images[0])
        print(f"✅ PDF převeden na obrázek: {image.shape}")
        
        return image
        
    except Exception as e:
        print(f"❌ Chyba při převodu PDF na obrázek: {e}")
        return None


def test_ocr_pipeline():
    """
    Hlavní test funkce pro OcrPipeline.
    """
    print("🚀 Test OcrPipeline")
    print("=" * 50)
    
    # Krok 1: Najdi první PDF soubor
    pdf_path = get_first_pdf_from_data()
    if not pdf_path:
        return
    
    # Krok 2: Převeď PDF na obrázek
    image = convert_pdf_to_image(pdf_path)
    if image is None:
        return
    
    # Krok 3: Inicializuj OcrPipeline
    print("\n🔧 Inicializuji OcrPipeline...")
    try:
        pipeline = OcrPipeline(lang="ces")
        print("✅ OcrPipeline inicializován")
    except Exception as e:
        print(f"❌ Chyba při inicializaci OcrPipeline: {e}")
        return
    
    # Krok 4: Zpracuj obrázek pomocí pipeline
    print("\n🔍 Zpracovávám obrázek pomocí OcrPipeline...")
    try:
        df = pipeline.process(image)
        print("✅ OCR zpracování dokončeno")
    except Exception as e:
        print(f"❌ Chyba při OCR zpracování: {e}")
        return
    
    # Krok 5: Zobraz statistiky
    print("\n📊 Statistiky výsledků:")
    print("-" * 30)
    
    if df is None or df.empty:
        print("❌ Žádné výsledky nebyly nalezeny!")
        return
    
    print(f"📝 Celkový počet textových prvků: {len(df)}")
    
    # Zobraz sloupce v DataFrame
    print(f"📋 Sloupce v DataFrame: {list(df.columns)}")
    
    # Zobraz první několik řádků
    print(f"\n📄 První 5 textových prvků:")
    for i, row in df.head().iterrows():
        text = row.get('text', 'N/A')
        bbox = row.get('bbox', 'N/A')
        print(f"  {i+1}. '{text}' - bbox: {bbox}")
    
    # Statistiky o délce textů
    if 'text' in df.columns:
        text_lengths = df['text'].str.len()
        print(f"\n📏 Statistiky délky textů:")
        print(f"  - Průměrná délka: {text_lengths.mean():.1f} znaků")
        print(f"  - Nejkratší text: {text_lengths.min()} znaků")
        print(f"  - Nejdelší text: {text_lengths.max()} znaků")
        
        # Zobraz nejdelší text
        longest_idx = text_lengths.idxmax()
        longest_text = df.loc[longest_idx, 'text']
        print(f"  - Nejdelší text: '{longest_text}'")
    
    print(f"\n✅ Test dokončen úspěšně!")
    print(f"📄 Zpracovaný soubor: {pdf_path}")
    print(f"📊 Nalezeno {len(df)} textových prvků")
    
    return df


if __name__ == "__main__":
    # Spusť test
    result_df = test_ocr_pipeline()
    
    if result_df is not None:
        print(f"\n🎉 Test proběhl úspěšně!")
        print(f"📋 DataFrame má {len(result_df)} řádků a {len(result_df.columns)} sloupců")
    else:
        print(f"\n❌ Test selhal!")
