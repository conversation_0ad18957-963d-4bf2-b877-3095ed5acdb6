#!/usr/bin/env python3
"""
Konfigurační soubor pro spuštění skriptů z IDE.

Upravte parametry podle potřeby a spusťte příslušný skript.
"""

# ========================================
# KONFIGURACE PRO process_document.py
# ========================================

class ProcessDocumentConfig:
    """Konfigurace pro zpracování jednoho dokumentu."""
    
    # Základní nastavení
    FILE_PATH = "Data/S3.pdf"          # Cesta k PDF dokumentu
    USE_GAT = False                    # True = použít GAT klasifikaci
    INTERACTIVE = False                # True = zobrazit interaktivní viewer
    
    # Alternativní soubory pro testování:
    # FILE_PATH = "Data/F1.pdf"
    # FILE_PATH = "Data/F2.pdf"
    # FILE_PATH = "Data/Pohoda01.pdf"


# ========================================
# KONFIGURACE PRO batch_process.py
# ========================================

class BatchProcessConfig:
    """Konfigurace pro batch zpracování."""
    
    # Možnost 1: Zpracovat konkrétní soubory
    FILES = ["Data/F1.pdf", "Data/F2.pdf"]  # Seznam konkrétních souborů
    
    # Možnost 2: Zpracovat celou složku (nastavte FILES = [] pro použití složky)
    # FILES = []
    FOLDER = "Data"                    # Složka s PDF soubory (použije se když FILES = [])
    
    # Další nastavení
    USE_GAT = False                    # True = použít GAT klasifikaci
    INTERACTIVE = False                # True = použít interaktivní viewer
    STOP_ON_ERROR = False              # True = zastavit při první chybě
    
    # Přednastavené konfigurace:
    
    @classmethod
    def quick_test(cls):
        """Rychlý test na jednom souboru."""
        cls.FILES = ["Data/S3.pdf"]
        cls.USE_GAT = False
        cls.INTERACTIVE = False
        cls.STOP_ON_ERROR = True
    
    @classmethod
    def full_batch(cls):
        """Zpracování všech souborů ve složce Data."""
        cls.FILES = []
        cls.FOLDER = "Data"
        cls.USE_GAT = False
        cls.INTERACTIVE = False
        cls.STOP_ON_ERROR = False
    
    @classmethod
    def interactive_batch(cls):
        """Batch s interaktivním režimem."""
        cls.FILES = ["Data/F1.pdf", "Data/F2.pdf"]
        cls.USE_GAT = False
        cls.INTERACTIVE = True
        cls.STOP_ON_ERROR = True
    
    @classmethod
    def gat_test(cls):
        """Test s GAT klasifikací."""
        cls.FILES = ["Data/S3.pdf"]
        cls.USE_GAT = True
        cls.INTERACTIVE = False
        cls.STOP_ON_ERROR = True


# ========================================
# POMOCNÉ FUNKCE
# ========================================

def run_process_document():
    """Spustí process_document.py s konfigurací."""
    import subprocess
    import sys
    
    config = ProcessDocumentConfig()
    
    print("🚀 Spouštím process_document.py s konfigurací:")
    print(f"📄 Soubor: {config.FILE_PATH}")
    print(f"🧠 GAT: {'Zapnuto' if config.USE_GAT else 'Vypnuto'}")
    print(f"🖱️  Interaktivní: {'Zapnuto' if config.INTERACTIVE else 'Vypnuto'}")
    print()
    
    # Sestavíme příkaz
    cmd = [sys.executable, "process_document.py", config.FILE_PATH]
    
    if config.USE_GAT:
        cmd.append("--gat")
    
    if not config.INTERACTIVE:
        cmd.append("--no-interactive")
    
    # Spustíme
    try:
        result = subprocess.run(cmd, check=True)
        print("✅ Zpracování dokončeno!")
    except subprocess.CalledProcessError as e:
        print(f"❌ Chyba při zpracování: {e}")
    except KeyboardInterrupt:
        print("\n⚠️  Zpracování přerušeno uživatelem")


def run_batch_process():
    """Spustí batch_process.py s konfigurací."""
    import subprocess
    import sys
    
    config = BatchProcessConfig()
    
    print("🚀 Spouštím batch_process.py s konfigurací:")
    if config.FILES:
        print(f"📄 Soubory: {config.FILES}")
    else:
        print(f"📁 Složka: {config.FOLDER}")
    print(f"🧠 GAT: {'Zapnuto' if config.USE_GAT else 'Vypnuto'}")
    print(f"🖱️  Interaktivní: {'Zapnuto' if config.INTERACTIVE else 'Vypnuto'}")
    print(f"🛑 Zastavit při chybě: {'Ano' if config.STOP_ON_ERROR else 'Ne'}")
    print()
    
    # Sestavíme příkaz
    cmd = [sys.executable, "batch_process.py"]
    
    if config.FILES:
        cmd.extend(config.FILES)
    else:
        cmd.extend(["--folder", config.FOLDER])
    
    if config.USE_GAT:
        cmd.append("--gat")
    
    if config.INTERACTIVE:
        cmd.append("--interactive")
    
    if config.STOP_ON_ERROR:
        cmd.append("--stop-on-error")
    
    # Spustíme
    try:
        result = subprocess.run(cmd, check=True)
        print("✅ Batch zpracování dokončeno!")
    except subprocess.CalledProcessError as e:
        print(f"❌ Chyba při batch zpracování: {e}")
    except KeyboardInterrupt:
        print("\n⚠️  Batch zpracování přerušeno uživatelem")


# ========================================
# HLAVNÍ FUNKCE PRO TESTOVÁNÍ
# ========================================

def main():
    """Hlavní funkce pro testování konfigurací."""
    print("🔧 Konfigurace pro spuštění z IDE")
    print("=" * 50)
    print()
    print("Dostupné možnosti:")
    print("1. Zpracování jednoho dokumentu")
    print("2. Batch zpracování")
    print("3. Rychlý test")
    print("4. Plný batch")
    print("5. Interaktivní batch")
    print("6. GAT test")
    print()
    
    choice = input("Vyberte možnost (1-6): ").strip()
    
    if choice == "1":
        run_process_document()
    elif choice == "2":
        run_batch_process()
    elif choice == "3":
        BatchProcessConfig.quick_test()
        run_batch_process()
    elif choice == "4":
        BatchProcessConfig.full_batch()
        run_batch_process()
    elif choice == "5":
        BatchProcessConfig.interactive_batch()
        run_batch_process()
    elif choice == "6":
        BatchProcessConfig.gat_test()
        run_batch_process()
    else:
        print("❌ Neplatná volba")


if __name__ == "__main__":
    main()
